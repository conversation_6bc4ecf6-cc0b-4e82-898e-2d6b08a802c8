import { acceptHMRUpdate, defineStore } from 'pinia'
import type {
  UserGamificationProfile,
  Achievement,
  UserAchievement,
  PointTransaction,
  Streak,
  GamificationStats,
  StreakData,
  GamificationDashboard,
  LeaderboardEntry,
} from '~/interfaces/gamification/gamification.interface'

export const useGamification = defineStore('gamification', () => {
  const { $api } = useNuxtApp()
  
  // State
  const profile = ref<UserGamificationProfile | null>(null)
  const achievements = ref<UserAchievement[]>([])
  const availableAchievements = ref<Achievement[]>([])
  const streaks = ref<Streak[]>([])
  const pointHistory = ref<PointTransaction[]>([])
  const leaderboard = ref<LeaderboardEntry[]>([])
  const userRank = ref<number>(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const currentLevel = computed(() => profile.value?.currentLevel || 1)
  const totalPoints = computed(() => profile.value?.totalPoints || 0)
  const pointsToNextLevel = computed(() => profile.value?.pointsToNextLevel || 100)
  const currentStreak = computed(() => profile.value?.currentStreak || 0)
  const longestStreak = computed(() => profile.value?.longestStreak || 0)
  
  const levelProgress = computed(() => {
    if (!profile.value) return { percentage: 0, current: 0, next: 100 }
    
    const current = profile.value.totalPoints
    const pointsForCurrentLevel = getLevelMinPoints(profile.value.currentLevel)
    const pointsForNextLevel = getLevelMinPoints(profile.value.currentLevel + 1)
    const progressInLevel = current - pointsForCurrentLevel
    const totalPointsForLevel = pointsForNextLevel - pointsForCurrentLevel
    
    return {
      percentage: Math.round((progressInLevel / totalPointsForLevel) * 100),
      current: progressInLevel,
      next: totalPointsForLevel,
    }
  })

  const recentAchievements = computed(() => {
    return achievements.value
      .sort((a, b) => new Date(b.earnedAt).getTime() - new Date(a.earnedAt).getTime())
      .slice(0, 5)
  })

  const dailyStreakData = computed((): StreakData | null => {
    const dailyStreak = streaks.value.find(s => s.streakType === 'daily_booking')
    if (!dailyStreak) return null

    // Generate last 7 days data
    const days = []
    const today = new Date()
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateString = date.toISOString().split('T')[0]
      
      // This is simplified - in reality you'd need to check actual booking data
      const isActive = i <= dailyStreak.currentCount - 1
      
      days.push({
        date: dateString,
        isActive,
        isToday: i === 0,
      })
    }

    return {
      type: 'Daily Booking',
      current: dailyStreak.currentCount,
      longest: dailyStreak.longestCount,
      isActive: dailyStreak.isActive,
      daysData: days,
    }
  })

  // Helper function to get minimum points for a level
  function getLevelMinPoints(level: number): number {
    const levels = [
      { level: 1, minPoints: 0 },
      { level: 2, minPoints: 100 },
      { level: 3, minPoints: 300 },
      { level: 4, minPoints: 600 },
      { level: 5, minPoints: 1000 },
      { level: 6, minPoints: 2000 },
    ]
    
    const levelInfo = levels.find(l => l.level === level)
    return levelInfo?.minPoints || 0
  }

  // Actions
  async function fetchProfile() {
    try {
      loading.value = true
      error.value = null
      profile.value = await $api.gamification.getUserProfile()
    } catch (err) {
      error.value = 'Failed to fetch gamification profile'
      console.error('Error fetching gamification profile:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchAchievements() {
    try {
      achievements.value = await $api.gamification.getUserAchievements()
      console.log('Fetched user achievements:', achievements.value)
    } catch (err) {
      console.error('Error fetching achievements:', err)
    }
  }

  async function fetchAvailableAchievements() {
    try {
      availableAchievements.value = await $api.gamification.getAvailableAchievements()
      console.log('Fetched available achievements:', availableAchievements.value)
    } catch (err) {
      console.error('Error fetching available achievements:', err)
    }
  }

  async function fetchStreaks() {
    try {
      streaks.value = await $api.gamification.getUserStreaks()
    } catch (err) {
      console.error('Error fetching streaks:', err)
    }
  }

  async function fetchPointHistory(limit: number = 50) {
    try {
      pointHistory.value = await $api.gamification.getPointHistory(limit)
    } catch (err) {
      console.error('Error fetching point history:', err)
    }
  }

  async function fetchLeaderboard(type: 'points' | 'streaks' = 'points', limit: number = 10) {
    try {
      leaderboard.value = await $api.gamification.getLeaderboard(type, limit)
    } catch (err) {
      console.error('Error fetching leaderboard:', err)
    }
  }

  async function fetchUserRank() {
    try {
      const response = await $api.gamification.getUserRank()
      userRank.value = response.rank
    } catch (err) {
      console.error('Error fetching user rank:', err)
    }
  }

  async function fetchStats(): Promise<GamificationStats | null> {
    try {
      return await $api.gamification.getUserStats()
    } catch (err) {
      console.error('Error fetching gamification stats:', err)
      return null
    }
  }

  async function fetchDashboardData(): Promise<GamificationDashboard | null> {
    try {
      loading.value = true
      error.value = null

      await Promise.all([
        fetchProfile(),
        fetchAchievements(),
        fetchStreaks(),
        fetchPointHistory(20),
        fetchUserRank(),
      ])

      if (!profile.value) return null

      return {
        profile: profile.value,
        streaks: dailyStreakData.value ? [dailyStreakData.value] : [],
        recentAchievements: recentAchievements.value,
        availableAchievements: [], // Would need to calculate progress
        pointsHistory: pointHistory.value,
        rank: userRank.value,
        levelProgress: {
          currentLevel: currentLevel.value,
          nextLevel: currentLevel.value + 1,
          currentPoints: totalPoints.value,
          pointsForNext: pointsToNextLevel.value,
          progressPercentage: levelProgress.value.percentage,
        },
      }
    } catch (err) {
      error.value = 'Failed to fetch gamification dashboard data'
      console.error('Error fetching gamification dashboard:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // Reset state
  function reset() {
    profile.value = null
    achievements.value = []
    availableAchievements.value = []
    streaks.value = []
    pointHistory.value = []
    leaderboard.value = []
    userRank.value = 0
    loading.value = false
    error.value = null
  }

  return {
    // State
    profile,
    achievements,
    availableAchievements,
    streaks,
    pointHistory,
    leaderboard,
    userRank,
    loading,
    error,

    // Getters
    currentLevel,
    totalPoints,
    pointsToNextLevel,
    currentStreak,
    longestStreak,
    levelProgress,
    recentAchievements,
    dailyStreakData,

    // Actions
    fetchProfile,
    fetchAchievements,
    fetchAvailableAchievements,
    fetchStreaks,
    fetchPointHistory,
    fetchLeaderboard,
    fetchUserRank,
    fetchStats,
    fetchDashboardData,
    reset,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useGamification, import.meta.hot))
}
