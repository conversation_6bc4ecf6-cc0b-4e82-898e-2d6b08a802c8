<template>
  <TairoModal
    :open="isOpen"
    size="lg"
    @close="$emit('close')"
  >
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <div>
          <BaseHeading
            as="h3"
            size="md"
            weight="medium"
            class="text-muted-900 dark:text-white"
          >
            Claim This Space
          </BaseHeading>
          <BaseText size="xs" class="text-muted-400">
            Submit a request to claim ownership of this space
          </BaseText>
        </div>
        <BaseButtonClose @click="$emit('close')" />
      </div>
    </template>

    <div class="p-4 md:p-6">
      <form class="space-y-4">
        <!-- Personal Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <BaseInput
            v-model="form.fullName"
            label="Full Name"
            placeholder="Enter your full name"
            :error="errors.fullName"
            required
          />
          <BaseInput
            v-model="form.email"
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            :error="errors.email"
            required
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <BaseInput
            v-model="form.phoneNumber"
            label="Phone Number"
            placeholder="+212 6XX XXX XXX"
            :error="errors.phoneNumber"
            required
          />
          <BaseInput
            v-model="form.position"
            label="Position/Role"
            placeholder="e.g., Owner, Manager"
            :error="errors.position"
            required
          />
        </div>

        <!-- Business Information -->
        <div class="border-t border-muted-200 dark:border-muted-700 pt-4">
          <h4 class="font-medium text-muted-800 dark:text-white mb-3">Business Information</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BaseInput
              v-model="form.businessName"
              label="Business Name"
              placeholder="Enter business/company name"
              :error="errors.businessName"
              required
            />
            <BaseInput
              v-model="form.businessRegistrationNumber"
              label="Registration Number"
              placeholder="Business registration or tax ID"
              :error="errors.businessRegistrationNumber"
            />
          </div>
        </div>

        <!-- Message -->
        <BaseTextarea
          v-model="form.message"
          label="Why are you claiming this space?"
          placeholder="Please explain your relationship to this space and why you should be granted ownership access..."
          rows="4"
          :error="errors.message"
          required
        />

        <!-- Document Upload Section -->
        <div class="border-t border-muted-200 dark:border-muted-700 pt-4">
          <h4 class="font-medium text-muted-800 dark:text-white mb-3">Supporting Documents (Optional)</h4>
          <p class="text-xs text-muted-500 mb-3">
            Upload documents to support your claim (business license, ID, proof of ownership, etc.)
          </p>
          
          <div class="grid grid-cols-1 gap-3">
            <BaseInput
              v-model="form.businessLicenseUrl"
              label="Business License"
              placeholder="Upload or paste URL to business license"
              :error="errors.businessLicenseUrl"
            />
            <BaseInput
              v-model="form.identificationDocumentUrl"
              label="Identification Document"
              placeholder="Upload or paste URL to ID document"
              :error="errors.identificationDocumentUrl"
            />
            <BaseInput
              v-model="form.proofOfOwnershipUrl"
              label="Proof of Ownership"
              placeholder="Upload or paste URL to ownership proof"
              :error="errors.proofOfOwnershipUrl"
            />
          </div>
        </div>

      </form>
    </div>

    <template #footer>
      <div class="p-4 md:p-6">
        <div class="flex items-center justify-end gap-3">
          <BaseButton
            type="button"
            variant="ghost"
            color="muted"
            @click="$emit('close')"
          >
            Cancel
          </BaseButton>
          <BaseButton
            type="button"
            color="primary"
            :loading="isSubmitting"
            :disabled="!isFormValid"
            @click="submitClaim"
          >
            <Icon name="ph:paper-plane-duotone" class="size-4" />
            Submit Claim Request
          </BaseButton>
        </div>
      </div>
    </template>
  </TairoModal>
</template>

<script setup lang="ts">
// import type { ClaimSpaceRequest } from '~/interfaces/space/claim.interface'

interface ClaimSpaceRequest {
  fullName: string;
  email: string;
  phoneNumber: string;
  position: string;
  businessName: string;
  businessRegistrationNumber?: string;
  message: string;
  businessLicenseUrl?: string;
  identificationDocumentUrl?: string;
  proofOfOwnershipUrl?: string;
}

interface Props {
  isOpen: boolean
  spaceId: string
  spaceName: string
}

interface Emits {
  (e: 'close'): void
  (e: 'success', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { $api } = useNuxtApp()
const toast = useToast()

// Form state
const form = ref({
  fullName: '',
  email: '',
  phoneNumber: '',
  position: '',
  businessName: '',
  businessRegistrationNumber: '',
  message: '',
  businessLicenseUrl: '',
  identificationDocumentUrl: '',
  proofOfOwnershipUrl: '',
})

const errors = ref({})
const isSubmitting = ref(false)

// Computed
const isFormValid = computed(() => {
  return form.value.fullName &&
         form.value.email &&
         form.value.phoneNumber &&
         form.value.position &&
         form.value.businessName &&
         form.value.message &&
         form.value.message.length >= 20
})

// Methods
const validateForm = () => {
  errors.value = {}

  if (!form.value.fullName) {
    errors.value.fullName = 'Full name is required'
  }

  if (!form.value.email) {
    errors.value.email = 'Email is required'
  }

  if (!form.value.phoneNumber) {
    errors.value.phoneNumber = 'Phone number is required'
  }

  if (!form.value.position) {
    errors.value.position = 'Position/role is required'
  }

  if (!form.value.businessName) {
    errors.value.businessName = 'Business name is required'
  }

  if (!form.value.message) {
    errors.value.message = 'Message is required'
  }

  return Object.keys(errors.value).length === 0
}

const submitClaim = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    const response = await $api.space.claimSpace(props.spaceId, form.value)

    console.log('Claim submitted successfully:', response)

    emit('success', response)
    emit('close')

    // Reset form
    Object.keys(form.value).forEach(key => {
      form.value[key] = ''
    })
  } catch (error: any) {
    console.error('Error submitting claim:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Reset errors when modal closes
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    errors.value = {}
  }
})
</script>
