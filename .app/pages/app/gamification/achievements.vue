<script setup lang="ts">
import { useGamification } from '~/stores/gamification'

definePageMeta({
  title: 'Achievements',
  preview: {
    title: 'Achievements',
    description: 'View your earned achievements and progress',
    categories: ['gamification'],
    order: 1,
  },
})

const gamificationStore = useGamification()
const loading = ref(true)
const selectedCategory = ref<string>('all')
const searchQuery = ref('')

const categories = [
  { value: 'all', label: 'All Achievements', icon: 'ph:trophy-duotone' },
  { value: 'booking', label: 'Booking', icon: 'ph:calendar-check-duotone' },
  { value: 'consistency', label: 'Consistency', icon: 'ph:fire-duotone' },
  { value: 'exploration', label: 'Exploration', icon: 'ph:compass-duotone' },
  { value: 'social', label: 'Social', icon: 'ph:users-duotone' },
]

const earnedAchievements = computed(() => gamificationStore.achievements)
const availableAchievements = computed(() => gamificationStore.availableAchievements)

const filteredAchievements = computed(() => {
  // Create a map of earned achievement IDs for quick lookup
  const earnedIds = new Set(earnedAchievements.value.map(ea => ea.achievement.id))

  // Filter available achievements to exclude already earned ones
  const unearned = availableAchievements.value.filter(a => !earnedIds.has(a.id))

  // Combine earned achievements and unearned available achievements
  let achievements = [...earnedAchievements.value, ...unearned]

  if (selectedCategory.value !== 'all') {
    achievements = achievements.filter(a =>
      'achievement' in a ? a.achievement.category === selectedCategory.value : a.category === selectedCategory.value
    )
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    achievements = achievements.filter(a => {
      const achievement = 'achievement' in a ? a.achievement : a
      return achievement.name.toLowerCase().includes(query) ||
             achievement.description.toLowerCase().includes(query)
    })
  }

  return achievements
})

const achievementStats = computed(() => {
  const total = availableAchievements.value.length + earnedAchievements.value.length
  const earned = earnedAchievements.value.length
  const percentage = total > 0 ? Math.round((earned / total) * 100) : 0
  
  return { total, earned, percentage }
})

onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([
      gamificationStore.fetchAchievements(),
      gamificationStore.fetchAvailableAchievements(),
    ])

    // Debug logging
    console.log('Earned achievements:', earnedAchievements.value)
    console.log('Available achievements:', availableAchievements.value)
    console.log('Filtered achievements:', filteredAchievements.value)
  } catch (error) {
    console.error('Failed to fetch achievements:', error)
  } finally {
    loading.value = false
  }
})

// Removed unused functions
</script>

<template>
  <div>
    <!-- Header -->
    <div class="mb-8">
      <BaseHeading
        as="h1"
        size="3xl"
        weight="light"
        lead="tight"
        class="text-muted-800 dark:text-white mb-2"
      >
        <span>Achievements</span>
      </BaseHeading>
      <BaseParagraph class="text-muted-500">
        Track your progress and unlock achievements by using the platform
      </BaseParagraph>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <BaseCard class="p-6 text-center">
        <div class="mb-4">
          <Icon name="ph:trophy-duotone" class="size-12 text-primary-500 mx-auto" />
        </div>
        <BaseHeading as="h3" size="2xl" weight="bold" class="text-muted-800 dark:text-white mb-1">
          {{ achievementStats.earned }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500">
          Achievements Earned
        </BaseParagraph>
      </BaseCard>

      <BaseCard class="p-6 text-center">
        <div class="mb-4">
          <Icon name="ph:target-duotone" class="size-12 text-success-500 mx-auto" />
        </div>
        <BaseHeading as="h3" size="2xl" weight="bold" class="text-muted-800 dark:text-white mb-1">
          {{ achievementStats.percentage }}%
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500">
          Completion Rate
        </BaseParagraph>
      </BaseCard>

      <BaseCard class="p-6 text-center">
        <div class="mb-4">
          <Icon name="ph:star-duotone" class="size-12 text-warning-500 mx-auto" />
        </div>
        <BaseHeading as="h3" size="2xl" weight="bold" class="text-muted-800 dark:text-white mb-1">
          {{ achievementStats.total }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500">
          Total Available
        </BaseParagraph>
      </BaseCard>
    </div>

    <!-- Filters -->
    <BaseCard class="p-6 mb-8">
      <div class="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div class="flex flex-wrap gap-2">
          <BaseButton
            v-for="category in categories"
            :key="category.value"
            size="sm"
            :variant="selectedCategory === category.value ? 'solid' : 'pastel'"
            :color="selectedCategory === category.value ? 'primary' : 'muted'"
            rounded="md"
            @click="selectedCategory = category.value"
          >
            <Icon :name="category.icon" class="size-4 mr-2" />
            {{ category.label }}
          </BaseButton>
        </div>
        
        <div class="w-full md:w-auto">
          <BaseInput
            v-model="searchQuery"
            placeholder="Search achievements..."
            icon="ph:magnifying-glass-duotone"
            rounded="md"
            class="w-full md:w-64"
          />
        </div>
      </div>
    </BaseCard>

    <!-- Achievements Grid -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="i in 9" :key="i" class="p-6 bg-white dark:bg-muted-800 rounded-lg">
        <div class="flex items-center justify-center mb-3">
          <BasePlaceload class="size-12 rounded-full" />
        </div>
        <BasePlaceload class="h-4 w-32 rounded mb-2 mx-auto" />
        <BasePlaceload class="h-3 w-full rounded mb-4" />
        <div class="flex items-center justify-between">
          <BasePlaceload class="h-6 w-16 rounded" />
          <BasePlaceload class="h-6 w-12 rounded" />
        </div>
      </div>
    </div>

    <div v-else-if="filteredAchievements.length === 0" class="text-center py-12">
      <Icon name="ph:trophy-duotone" class="size-16 text-muted-400 mx-auto mb-4" />
      <BaseHeading as="h3" size="lg" class="text-muted-600 dark:text-muted-400 mb-2">
        No achievements found
      </BaseHeading>
      <BaseParagraph class="text-muted-500">
        Try adjusting your filters or search query
      </BaseParagraph>

      <!-- Debug info -->
      <div class="mt-4 text-xs text-muted-400">
        <p>Debug: Earned: {{ earnedAchievements.length }}, Available: {{ availableAchievements.length }}</p>
      </div>
    </div>

    <!-- Debug: Show earned achievements directly -->
    <div v-if="earnedAchievements.length > 0" class="mb-8">
      <BaseHeading as="h3" size="lg" class="text-muted-800 dark:text-white mb-4">
        Your Earned Achievements ({{ earnedAchievements.length }})
      </BaseHeading>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AchievementBadge
          v-for="userAchievement in earnedAchievements"
          :key="userAchievement.id"
          :achievement="userAchievement.achievement"
          :is-earned="true"
          :earned-at="userAchievement.earnedAt"
        />
      </div>
    </div>

    <!-- Original filtered achievements -->
    <div v-if="filteredAchievements.length > 0" class="mb-8">
      <BaseHeading as="h3" size="lg" class="text-muted-800 dark:text-white mb-4">
        All Achievements ({{ filteredAchievements.length }})
      </BaseHeading>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AchievementBadge
          v-for="item in filteredAchievements"
          :key="'achievement' in item ? item.achievement.id : item.id"
          :achievement="'achievement' in item ? item.achievement : item"
          :is-earned="'achievement' in item"
          :earned-at="'achievement' in item ? item.earnedAt : undefined"
          :progress="'achievement' in item ? undefined : { currentProgress: 0, targetProgress: 1 }"
        />
      </div>
    </div>
  </div>
</template>
