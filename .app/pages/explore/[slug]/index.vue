<script setup lang="ts">
import { useIntersectionObserver, useUrlSearchParams, useTitle } from '@vueuse/core'
import { Calendar } from 'v-calendar'
import type { SpaceResponse,SpaceAvailabilityRequest, SpaceAvailabilityResponse, CheckRoomAvailabilityResponse, CheckRoomAvailabilityRequest } from '~/interfaces/space/space.interface'
import type { InitialeBookingRequest, InitialeBookingResponse } from '~/interfaces/booking'
import type { Review } from '~/types'
import type { Event } from '~/types/event/event.type'
import { EventStatusEnum } from '~/types/event/event.type'
import { QuoteStatus } from '~/interfaces/quote'
import { format } from 'date-fns'
import LocationMap from '~/components/LocationMap.vue'

import 'v-calendar/dist/style.css'
const { $api } = useNuxtApp(); 
const router = useRouter(); // Add this line to get the router instance
const title = useTitle()

// Add import for useUserSession
const userSession = useUserSession()

definePageMeta({
  title: 'Workspace Details',
  layout: 'default',
  preview: {
    title: 'Coworking Space',
    description: 'View and book coworking spaces',
    categories: ['spaces'],
    src: '/img/screens/space-details.png',
    srcDark: '/img/screens/space-details-dark.png',
    order: 23,
  },
})

// State management
const { open, close } = usePanels()
const isHeaderSticky = ref(false)
const bookingRef = ref<HTMLElement | null>(null)
const headerRef = ref<HTMLElement | null>(null)
const activeTab = ref('overview')
const showAllAmenities = ref(false)
const expandedCategories = ref([])
const showAllPhotos = ref(false)
const activeImageIndex = ref(0)

// New code for gallery functionality
const isZoomed = ref(false)
const isTransitioning = ref(false)
const touchStartX = ref(0)
const touchEndX = ref(0)

// Bookmark state
const isBookmarked = ref(false)
const isBookmarkLoading = ref(false)

// Booking state
const selectedRoom = ref(null)
const bookingType = ref('daily') // 'daily' or 'hourly'
const selectedDates = ref({
  checkIn: null,
  checkOut: null
})
const selectedTimes = ref({
  startTime: '09:00',
  endTime: '17:00'
})
const guestCount = ref(1)
const isCheckInOpen = ref(false)
const isCheckOutOpen = ref(false)
const addOns = ref({
  coffee: false,
  meetingRoom: false,
  printing: false,
  locker: false
})

// State for data loading
const pending = ref(true);
const error = ref(null);
const spaceData = ref(null);
const memberships = ref([]);
const pendingMemberships = ref(true);
const hasMemberships = computed(() => memberships.value.length > 0);

// Events state
const events = ref<Event[]>([]);
const pendingEvents = ref(true);
const eventsError = ref(null);
// Get the slug from the route
const route = useRoute();
const slug = computed(() => route.params.slug as string);

// Add new refs for availability state
const isCheckingAvailability = ref(false);
const isAvailable = ref(false);

// Review guidelines state
const showGuidelinesExpanded = ref(true); // Changed to true for expanded by default

// Location map ref
const locationMapRef = ref()

// Transport styling helpers
function getTransportIcon(type: string) {
  switch (type) {
    case 'train': return 'ph:train'
    case 'tram': return 'ph:train'
    case 'bus':
    default: return 'ph:bus'
  }
}

function getTransportBgColor(type: string) {
  switch (type) {
    case 'train': return 'bg-blue-600 dark:bg-blue-500'
    case 'tram': return 'bg-emerald-600 dark:bg-emerald-500'
    case 'bus':
    default: return 'bg-amber-600 dark:bg-amber-500'
  }
}

function getTransportTextColor(type: string) {
  return 'text-white'
}

// URL Search Params
const params = useUrlSearchParams('history', {
  initialValue: {
    tab: 'overview',
    bookingType: 'daily',
    startTime: '09:00',
    endTime: '17:00',
    guests: '1',
    room: ''
  }
})

// Initialize state from URL params
onMounted(() => {
  if (params.tab) activeTab.value = params.tab
  if (params.bookingType) bookingType.value = params.bookingType
  if (params.startTime) selectedTimes.value.startTime = params.startTime
  if (params.endTime) selectedTimes.value.endTime = params.endTime
  if (params.guests) guestCount.value = parseInt(params.guests)
  if (params.room && spaceData.value?.space?.rooms) {
    const room = spaceData.value.space.rooms.find(r => r.id === params.room)
    if (room) selectedRoom.value = room
  }
})

// Watch for URL param changes and update local state
watch(() => params.tab, (newTab) => {
  if (newTab) activeTab.value = newTab
}, { immediate: true })

watch(() => params.bookingType, (newType) => {
  if (newType) bookingType.value = newType
}, { immediate: true })

watch(() => params.guests, (newGuests) => {
  if (newGuests) guestCount.value = parseInt(newGuests)
}, { immediate: true })

watch(() => params.room, (newRoom) => {
  if (newRoom && spaceData.value?.space?.rooms) {
    const room = spaceData.value.space.rooms.find(r => r.id === newRoom)
    if (room) selectedRoom.value = room
  }
}, { immediate: true })

// Update URL params when local state changes
watch(activeTab, (newTab) => {
  params.tab = newTab
})

watch(bookingType, (newType) => {
  params.bookingType = newType
})

watch(guestCount, (newCount) => {
  params.guests = newCount.toString()
})

watch(selectedRoom, (newRoom) => {
  params.room = newRoom?.id || ''
})

// Fetch space data using the API
const fetchSpace = async () => {
  try {
    pending.value = true;
    error.value = null;
    
    const response = await $api.space.getSpaceBySlug(slug.value);
    
    // Transform the response data to ensure it has the expected structure
    spaceData.value = {
      space: {
        ...response,
        // Transform images array to gallery array of image paths
        gallery: response.images ? response.images.map(img => {
          // Fix double URL prefix issue in image paths
          let path = img.path;
          if (path.startsWith('http://localhost:4000http://localhost:4000')) {
            path = path.replace('http://localhost:4000http://localhost:4000', 'http://localhost:4000');
          }
          return path;
        }) : [],
        // Set main image from the first image
        mainImage: response.images && response.images.length > 0 ? 
          (response.images[0].path.startsWith('http://localhost:4000http://localhost:4000') ? 
            response.images[0].path.replace('http://localhost:4000http://localhost:4000', 'http://localhost:4000') : 
            response.images[0].path) : 
          '',
        // Add default operating hours if not provided
        hours: response.hours || {
          monday: '09:00 - 17:00',
          tuesday: '09:00 - 17:00',
          wednesday: '09:00 - 17:00',
          thursday: '09:00 - 17:00',
          friday: '09:00 - 17:00',
          saturday: 'Closed',
          sunday: 'Closed'
        },
        // Add default features if not provided
        features: response.features || [
          {
            title: 'Workspace Capacity',
            description: 'Maximum occupancy',
            icon: 'ph:users-duotone',
            value: `${response.rooms?.[0]?.capacity || 20} people`,
            utilization: '60%'
          },
          {
            title: 'Desk Availability',
            description: 'Open workstations',
            icon: 'ph:desktop-duotone',
            value: 'Hot desks',
            utilization: '45%'
          },
          {
            title: 'Meeting Rooms',
            description: 'Bookable spaces',
            icon: 'ph:presentation-chart-duotone',
            value: '3 rooms',
            utilization: '75%'
          },
          {
            title: 'Internet Speed',
            description: 'Fiber connection',
            icon: 'ph:wifi-high-duotone',
            value: '1 Gbps',
            utilization: '30%'
          }
        ],
        // Ensure amenities is an array
        amenities: response.amenities || []
      }
    };
    
    console.log('Transformed space data:', spaceData.value);
    title.value = `${spaceData.value.space.name} - ${spaceData.value.space.address}`
    // Set initial selected room if available
    if (spaceData.value?.space?.rooms?.length > 0) {
      selectedRoom.value = spaceData.value.space.rooms[0];
    }
    
    // Check if the space is already bookmarked
    if (userSession.isLoggedIn) {
      checkBookmarkStatus();
    }
  } catch (err) {
    console.error('Error fetching space:', err);
    error.value = err;
  } finally {
    pending.value = false;
  }
};

const fetchSpaceMemberships = async () => {
  try {
    pendingMemberships.value = true;
    const response = await $api.space.getSpaceMemberships(spaceData.value.space.id);
    memberships.value = response;
    console.log('Space memberships:', memberships.value);
    pendingMemberships.value = false;
  } catch (err) {
    console.error('Error fetching space memberships:', err);
    error.value = err;
    pendingMemberships.value = false;
  }
};

const fetchSpaceEvents = async () => {
  try {
    pendingEvents.value = true;
    eventsError.value = null;

    // Fetch upcoming events for this space
    const response = await $api.event.getSpaceEvents(spaceData.value.space.id, {
      status: EventStatusEnum.PUBLISHED,
      upcomingOnly: true,
      limit: 6,
      sortBy: 'startDateTime',
      sortOrder: 'ASC'
    });

    events.value = response.data || [];
    console.log('Space events:', events.value);
  } catch (err) {
    console.error('Error fetching space events:', err);
    eventsError.value = err;
    events.value = [];
  } finally {
    pendingEvents.value = false;
  }
};

watch(spaceData, () => {
  if (spaceData.value?.space?.id) {
    fetchSpaceMemberships();
    fetchReviews();
    fetchSpaceEvents();
  }
}, { immediate: true });




const isModalOpen = ref(false)

function closeModal() {
  isModalOpen.value = false
}
function openModal() {
  isModalOpen.value = true
}

const isModalMdOpen = ref(false)

const reviews = ref<Review[]>([]);

// Review statistics
const reviewStats = ref(null);
watch(reviews, (newReviews, oldReviews) => {
  console.log('Watcher triggered!');
  console.log('Old reviews:', oldReviews);
  console.log('New reviews:', newReviews);
  console.log('New reviews length:', newReviews?.length);
  
  if (!newReviews || newReviews.length === 0) {
    console.log('No reviews found in watcher');
    reviewStats.value = {
      averageRating: '0.0',
      totalReviews: 0,
      ratingDistribution: {
        5: 0,
        4: 0,
        3: 0,
        2: 0,
        1: 0
      },
      categories: {
        cleanliness: '0.0',
        amenities: '0.0',
        location: '0.0',
        valueForMoney: '0.0'
      }
    };
    return;
  }
  
  console.log('Processing reviews in watcher:', newReviews);
  
  // Calculate stats here and assign to reviewStats.value
  const totalReviews = reviews.value.length;
  const totalRating = reviews.value.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / totalReviews;

  // Calculate rating distribution
  const ratingDistribution = {
    5: reviews.value.filter(r => r.rating === 5).length,
    4: reviews.value.filter(r => r.rating === 4).length,
    3: reviews.value.filter(r => r.rating === 3).length,
    2: reviews.value.filter(r => r.rating === 2).length,
    1: reviews.value.filter(r => r.rating === 1).length
  };

  // Calculate category averages - updated to match your data structure
  const categoryTotals = {
    cleanliness: 0,
    amenities: 0,
    location: 0,
    valueForMoney: 0
  };

  reviews.value.forEach(review => {
    categoryTotals.cleanliness += review.cleanliness || 0;
    categoryTotals.amenities += review.amenities || 0;
    categoryTotals.location += review.location || 0;
    categoryTotals.valueForMoney += review.valueForMoney || 0;
  });

  const categories = {
    cleanliness: (categoryTotals.cleanliness / totalReviews).toFixed(1),
    amenities: (categoryTotals.amenities / totalReviews).toFixed(1),
    location: (categoryTotals.location / totalReviews).toFixed(1),
    valueForMoney: (categoryTotals.valueForMoney / totalReviews).toFixed(1)
  };

  reviewStats.value = {
    averageRating: averageRating.toFixed(1),
    totalReviews,
    ratingDistribution,
    categories
  };
});


// Fetch reviews for the space
const fetchReviews = async () => {
  try {
    console.log('Fetching reviews for space ID:', spaceData.value.space.id);
    const { data } = await $api.review.getSpaceReviews(spaceData.value.space.id);
    console.log('API response:', data);
    
    // Fix: Use data directly instead of data.reviews
    reviews.value = data; // Changed from data.reviews to data
    console.log('Reviews.value after assignment:', reviews.value);
    console.log('Reviews.value length after assignment:', reviews.value?.length);
  } catch (err) {
    console.error('Error fetching reviews:', err);
    error.value = err;
  }
};

// Check if the space is bookmarked
const checkBookmarkStatus = async () => {
  try {
    // Get user bookmarks
    const { data } = await $api.bookmark.getUserBookmarks();
    
    // Check if current space is in user's bookmarks
    if (data && Array.isArray(data)) {
      isBookmarked.value = data.some(bookmark => bookmark.space?.id === spaceData.value.space.id);
    }
  } catch (err) {
    console.error('Error checking bookmark status:', err);
  }
};

// Add validation for time selection
const isValidTimeRange = computed(() => {
  if (bookingType.value !== 'hourly') return true;
  if (!selectedTimes.value?.startTime || !selectedTimes.value?.endTime) return false;

  const [startHour] = selectedTimes.value.startTime.split(':').map(Number);
  const [endHour] = selectedTimes.value.endTime.split(':').map(Number);

  return startHour < endHour;
});

// Update isValidBooking to include time validation
const isValidBooking = computed(() => {
  return hasSelectedDates.value && 
         guestCount.value > 0 && 
         selectedRoom.value &&
         isValidTimeRange.value &&
         isAvailable.value;
});

// Add debounce utility function
const debounce = (fn: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

// Create debounced version of checkAvailability
const debouncedCheckAvailability = debounce(async () => {
  if (!selectedDates.value?.checkIn || 
      !selectedDates.value?.checkOut || 
      !isValidTimeRange.value) {
    return;
  }

  try {
    isCheckingAvailability.value = true;
    error.value = null;
    
    const request: CheckRoomAvailabilityRequest = {
      id: selectedRoom.value?.id,
      startDate: formatDateForApi(
        selectedDates.value.checkIn,
        bookingType.value === 'hourly' ? selectedTimes.value?.startTime : undefined
      ),
      endDate: formatDateForApi(
        selectedDates.value.checkOut,
        bookingType.value === 'hourly' ? selectedTimes.value?.endTime : undefined
      ),
      quantity: guestCount.value
    };
    
    // Validate time range before making the request
    if (bookingType.value === 'hourly') {
      const startDate = new Date(request.startDate);
      const endDate = new Date(request.endDate);
      
      if (startDate >= endDate) {
        throw new Error('Invalid time range: Start time must be before end time');
      }
    }
    
    const response: CheckRoomAvailabilityResponse = await $api.availability.checkRoomAvailability(request);
    isAvailable.value = response.isAvailable;
    
    console.log('Room availability response:', response);
    
  } catch (err) {
    console.error('Error checking room availability:', err);
    error.value = err;
    isAvailable.value = false;
  } finally {
    isCheckingAvailability.value = false;
  }
}, 1000); // 1000ms debounce delay

// bookSpace function to handle booking
const bookSpace = async () => {
  try {
    // Prepare the booking request
    const request: InitialeBookingRequest = {
      room:{
        id: selectedRoom.value?.id
      },
      bookingDate: formatDateForApi(new Date()),
      startDateTime: formatDateForApi(
        selectedDates.value.checkIn,
        bookingType.value === 'hourly' ? selectedTimes.value.startTime : undefined
      ),
      endDateTime: formatDateForApi(
        selectedDates.value.checkOut,
        bookingType.value === 'hourly' ? selectedTimes.value.endTime : undefined
      ),
      quantity: guestCount.value,
    };
    
    // Make the booking request
    const response: InitialeBookingResponse = await $api.booking.initiateBooking(request);
    
    console.log('Booking response:', response);

    // Navigate to checkout with booking details
    await navigateTo({
      path: `/app/booking/checkout/${response.bookingId}`
    });
  } catch (err) {
    console.error('Error booking space:', err);
    error.value = err;
  }
};

// Fetch data on component mount
onMounted(async () => {
  await fetchSpace();
  await fetchReviews();
  // Event listeners for keyboard navigation
  window.addEventListener('keydown', handleKeydown);
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})

function openCreateReviewPanel() {
  console.log('spaceData.value.space', spaceData.value.space)
  open('create-review', {
    space: spaceData.value.space,
    onMessage: async (message: any) => {
      console.log('Review created:', message);

      if (message.type === 'success') {
        // Show success toast notification
        const toaster = useToaster();
        toaster.show({
          title: 'Review Submitted!',
          message: 'Thank you for sharing your experience. Your review has been posted successfully.',
          color: 'success',
          icon: 'ph:check-circle',
          closable: true,
        });

        // Refresh reviews to show the new review
        await fetchReviews();
      }
    },
  })
}

// Toggle review guidelines
function toggleReviewGuidelines() {
  showGuidelinesExpanded.value = !showGuidelinesExpanded.value;
}

// Computed properties
const allPhotos = computed(() => {
  if (!spaceData.value?.space) return [];
  const mainImage = spaceData.value.space.mainImage || '';
  const gallery = spaceData.value.space.gallery || [];
  
  // Filter out any empty strings or null values
  return [mainImage, ...gallery].filter(Boolean);
})

const totalNights = computed(() => {
  if (!selectedDates.value.checkIn || !selectedDates.value.checkOut) return 0
  const diffTime = selectedDates.value.checkOut.getTime() - selectedDates.value.checkIn.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

const calculateHours = computed(() => {
  if (!selectedTimes.value.startTime || !selectedTimes.value.endTime) return 0
  const startHour = parseInt(selectedTimes.value.startTime.split(':')[0])
  const endHour = parseInt(selectedTimes.value.endTime.split(':')[0])
  return endHour - startHour
})

const totalPrice = computed(() => {
  if (!selectedRoom.value) return { subtotal: 0, cleaningFee: 0, serviceFee: 0, addOnsCost: 0, total: 0 }
  
  let basePrice = 0
  if (bookingType.value === 'hourly') {
    basePrice = selectedRoom.value.pricePerHour * calculateHours.value
  } else {
    basePrice = selectedRoom.value.pricePerDay * totalNights.value
  }
  
  const cleaningFee = 2.5
  const serviceFee = Math.round(basePrice * 0.15)
  
  // Calculate add-ons total
  const addOnsCost = Object.entries(addOns.value).reduce((total, [key, isSelected]) => {
    return total + (isSelected ? workspaceAddons[key].price : 0)
  }, 0)

  return {
    subtotal: basePrice,
    cleaningFee,
    serviceFee,
    addOnsCost,
    total: basePrice + cleaningFee + serviceFee + addOnsCost
  }
})

// Add a computed property to check if dates are selected
const hasSelectedDates = computed(() => {
  return selectedDates.value?.checkIn && selectedDates.value?.checkOut;
});

// Booking calendar attributes
const bookingCalendarAttrs = computed(() => {
  const attrs = []

  // Today's date
  attrs.push({
    key: 'today',
    dot: {
      color: 'primary',
    },
    dates: new Date()
  })

  // Selected range
  if (selectedDates.value.checkIn && selectedDates.value.checkOut) {
    attrs.push({
      key: 'range',
      highlight: {
        start: { fillMode: 'solid' },
        base: { fillMode: 'light' },
        end: { fillMode: 'solid' },
      },
      dates: { 
        start: selectedDates.value.checkIn,
        end: selectedDates.value.checkOut
      }
    })
  }

  return attrs
})

// Quote calendar attributes
const quoteCalendarAttrs = computed(() => {
  const attrs = []

  // Today's date
  attrs.push({
    key: 'today',
    dot: {
      color: 'primary',
    },
    dates: new Date()
  })

  // Selected range
  if (quoteForm.startDate && quoteForm.endDate) {
    attrs.push({
      key: 'range',
      highlight: {
        start: { fillMode: 'solid' },
        base: { fillMode: 'light' },
        end: { fillMode: 'solid' },
      },
      dates: { 
        start: quoteForm.startDate,
        end: quoteForm.endDate
      }
    })
  }

  return attrs
})

// Tour calendar attributes
const tourCalendarAttrs = computed(() => {
  const attrs = []

  // Today's date
  attrs.push({
    key: 'today',
    dot: {
      color: 'primary',
    },
    dates: new Date()
  })

  // Selected date
  if (tourForm.value.date) {
    attrs.push({
      key: 'selected',
      highlight: {
        start: { fillMode: 'solid' },
        base: { fillMode: 'solid' },
        end: { fillMode: 'solid' },
      },
      dates: tourForm.value.date
    })
  }

  return attrs
})

// Computed property for available start times
const availableStartTimes = computed(() => {
  if (!selectedDates.value.checkIn) return [];

  const isToday = isDateToday(selectedDates.value.checkIn);
  const currentHour = isToday ? new Date().getHours() : 0;
  const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][selectedDates.value.checkIn.getDay()];
  const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];

  if (!operatingHours?.isOpen) return [];

  const [openHour] = operatingHours.open.split(':').map(Number);
  const [closeHour] = operatingHours.close.split(':').map(Number);
  
  // Start from the current hour if it's today and after opening time
  const startHour = isToday ? Math.max(openHour, currentHour) : openHour;
  
  const times = [];
  for (let hour = startHour; hour < closeHour; hour++) {
    times.push({
      value: `${String(hour).padStart(2, '0')}:00`,
      label: formatTime(`${hour}:00`)
    });
  }
  
  return times;
});

const availableEndTimes = computed(() => {
  if (!selectedDates.value.checkIn || !selectedTimes.value.startTime) return [];
  
  const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][selectedDates.value.checkIn.getDay()];
  const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];
  
  if (!operatingHours?.isOpen) return [];
  
  const startHour = parseInt(selectedTimes.value.startTime.split(':')[0]);
  const [closeHour] = operatingHours.close.split(':').map(Number);
  
  // Generate time slots from one hour after start time to closing time
  const slots = [];
  for (let hour = startHour + 1; hour <= closeHour; hour++) {
    const time = `${hour.toString().padStart(2, '0')}:00`;
    slots.push({
      label: formatTimeLabel(time),
      value: time
    });
  }
  return slots;
});

// Computed property for amenity categories
const amenityCategories = computed(() => {
  if (!spaceData.value?.space?.amenities) return [];
  
  const amenities = spaceData.value.space.amenities;
  
  return [
    {
      name: 'Workspace Essentials',
      icon: 'ph:desktop-duotone',
      items: amenities.filter(a => 
        ['WiFi', 'Power Outlets', 'Standing Desks', 'Dedicated Desks', 'Open Seating', 
         'Monitors', 'Whiteboards', 'Air Conditioning', 'Heating'].includes(a.name))
    },
    {
      name: 'Security & Access',
      icon: 'ph:shield-check-duotone',
      items: amenities.filter(a => 
        ['Security Cameras', '24/7 Access', 'Reception', 'Wheelchair Accessible', 
         'Elevator', 'Virtual Receptionist', 'On-site Staff'].includes(a.name))
    },
    {
      name: 'Meeting Facilities',
      icon: 'ph:users-three-duotone',
      items: amenities.filter(a => 
        ['Conference Rooms', 'Meeting Rooms', 'Phone Booths', 'Private Offices',
         'Video Conferencing', 'Projectors', 'Audio/Visual Equipment'].includes(a.name))
    },
    {
      name: 'Comfort & Amenities',
      icon: 'ph:coffee-duotone',
      items: amenities.filter(a => 
        ['Coffee/Tea', 'Kitchen', 'Lounge Areas', 'Break Room', 'Snacks', 
         'Filtered Water', 'Refrigerator', 'Microwave', 'Vending Machines'].includes(a.name))
    },
    {
      name: 'Storage & Services',
      icon: 'ph:package-duotone',
      items: amenities.filter(a => 
        ['Lockers', 'Printer/Scanner', 'Mail Handling', 'Business Address',
         'Virtual Mailbox', 'Courier Services', 'Office Supplies'].includes(a.name))
    },
    {
      name: 'Wellness & Recreation',
      icon: 'ph:heartbeat-duotone',
      items: amenities.filter(a => 
        ['Outdoor Space', 'Fitness Center', 'Gaming Area'].includes(a.name))
    },
    {
      name: 'Event & Training',
      icon: 'ph:presentation-duotone',
      items: amenities.filter(a => 
        ['Event Space', 'Classrooms', 'Lecture Halls', 'Workshop Space',
         'Stage', 'Sound System', 'Lighting Equipment', 'Catering', 'Event Planning'].includes(a.name))
    },
    {
      name: 'Digital Services',
      icon: 'ph:cloud-duotone',
      items: amenities.filter(a => 
        ['Cloud Storage', 'Virtual Whiteboard', 'Screen Sharing', 'Collaboration Tools',
         'Online Booking System', 'Technical Support', 'IT Services'].includes(a.name))
    },
    {
      name: 'Professional Services',
      icon: 'ph:briefcase-duotone',
      items: amenities.filter(a => 
        ['Business Consultation', 'Networking Events', 'Workshops and Seminars',
         'Mentorship Programs', 'Legal Services', 'Accounting Services', 'Phone Answering Service'].includes(a.name))
    },
    {
      name: 'Facilities',
      icon: 'ph:house-duotone',
      items: amenities.filter(a => 
        ['Parking', 'Restrooms', 'Cleaning Services'].includes(a.name))
    },
    {
      name: 'Other Amenities',
      icon: 'ph:plus-circle-duotone',
      items: amenities.filter(a => 
        !['WiFi', 'Power Outlets', 'Standing Desks', 'Dedicated Desks', 'Open Seating', 
          'Monitors', 'Whiteboards', 'Air Conditioning', 'Heating', 'Security Cameras', 
          '24/7 Access', 'Reception', 'Wheelchair Accessible', 'Elevator', 'Virtual Receptionist', 
          'On-site Staff', 'Conference Rooms', 'Meeting Rooms', 'Phone Booths', 'Private Offices',
          'Video Conferencing', 'Projectors', 'Audio/Visual Equipment', 'Coffee/Tea', 'Kitchen', 
          'Lounge Areas', 'Break Room', 'Snacks', 'Filtered Water', 'Refrigerator', 'Microwave', 
          'Vending Machines', 'Lockers', 'Printer/Scanner', 'Mail Handling', 'Business Address',
          'Virtual Mailbox', 'Courier Services', 'Office Supplies', 'Outdoor Space', 'Fitness Center', 
          'Gaming Area', 'Event Space', 'Classrooms', 'Lecture Halls', 'Workshop Space',
          'Stage', 'Sound System', 'Lighting Equipment', 'Catering', 'Event Planning',
          'Cloud Storage', 'Virtual Whiteboard', 'Screen Sharing', 'Collaboration Tools',
          'Online Booking System', 'Technical Support', 'IT Services', 'Business Consultation', 
          'Networking Events', 'Workshops and Seminars', 'Mentorship Programs', 'Legal Services', 
          'Accounting Services', 'Phone Answering Service', 'Parking', 'Restrooms', 'Cleaning Services'
        ].includes(a.name))
    }
  ].filter(category => category.items.length > 0); // Only include categories with items
});

// Workspace add-ons
const workspaceAddons = {
  coffee: {
    name: 'Premium Coffee & Tea',
    description: 'Unlimited premium beverages throughout your stay',
    price: 50,
    icon: 'ph:coffee-duotone'
  },
  meetingRoom: {
    name: 'Meeting Room Access',
    description: '2 hours of meeting room usage per day',
    price: 150,
    icon: 'ph:projector-screen-chart-duotone'
  },
  printing: {
    name: 'Print & Scan Package',
    description: '100 pages of printing/scanning credits',
    price: 75,
    icon: 'ph:printer-duotone'
  },
  locker: {
    name: 'Personal Locker',
    description: 'Secure storage for your belongings',
    price: 50,
    icon: 'ph:lock-key-duotone'
  }
}

// Filter only active memberships
const activeMemberships = computed(() => {
  return memberships.value?.filter(membership => membership.isActive) || []
})

// Helper functions for styling based on membership type
const getMembershipHeaderClasses = (type) => {
  const baseClasses = 'bg-gradient-to-r'
  if (type.toLowerCase() === 'monthly') {
    return `${baseClasses} from-primary-50 to-primary-100 dark:from-primary-500/10 dark:to-primary-500/20 border-primary-100 dark:border-primary-500/20`
  } else if (type.toLowerCase() === 'yearly') {
    return `${baseClasses} from-success-50 to-success-100 dark:from-success-500/10 dark:to-success-500/20 border-success-100 dark:border-success-500/20`
  }
  return `${baseClasses} from-muted-50 to-muted-100 dark:from-muted-500/10 dark:to-muted-500/20 border-muted-100 dark:border-muted-500/20`
}

const getMembershipTitleClasses = (type) => {
  if (type.toLowerCase() === 'monthly') {
    return 'text-primary-600 dark:text-primary-400'
  } else if (type.toLowerCase() === 'yearly') {
    return 'text-success-600 dark:text-success-400'
  }
  return 'text-muted-600 dark:text-muted-400'
}

const getMembershipBadgeClasses = (type) => {
  if (type.toLowerCase() === 'monthly') {
    return 'bg-primary-500/10 text-primary-600 dark:text-primary-400'
  } else if (type.toLowerCase() === 'yearly') {
    return 'bg-success-500/10 text-success-600 dark:text-success-400'
  }
  return 'bg-muted-500/10 text-muted-600 dark:text-muted-400'
}

const getMembershipButtonColor = (type) => {
  if (type.toLowerCase() === 'monthly') {
    return 'primary'
  } else if (type.toLowerCase() === 'yearly') {
    return 'success'
  }
  return 'default'
}

const getMembershipDescriptionClasses = (type) => {
  if (type.toLowerCase() === 'monthly') {
    return 'text-muted-600 dark:text-muted-400'
  } else if (type.toLowerCase() === 'yearly') {
    return 'text-success-700 dark:text-success-300'
  }
  return 'text-muted-600 dark:text-muted-400'
}

const getMembershipBadgeText = (type, index) => {
  if (type.toLowerCase() === 'monthly') {
    return 'Most Popular'
  } else if (type.toLowerCase() === 'yearly') {
    return 'Best Value'
  }
  return index === 0 ? 'Popular' : 'Value'
}

const getMembershipDescription = (membership) => {
  if (membership.type.toLowerCase() === 'monthly') {
    return 'Flexible option with month-to-month commitment'
  } else if (membership.type.toLowerCase() === 'yearly') {
    // Calculate savings if you have monthly price for comparison
    const monthlySavings = calculateYearlySavings(membership.price)
    return monthlySavings ? `Save ${formatPrice(monthlySavings)} compared to monthly` : 'Best value for long-term commitment'
  }
  return 'Professional workspace solution'
}

const calculateYearlySavings = (yearlyPrice) => {
  // Find monthly membership to calculate savings
  const monthlyMembership = activeMemberships.value.find(m => m.type.toLowerCase() === 'monthly')
  if (monthlyMembership) {
    const monthlyTotal = parseFloat(monthlyMembership.price) * 12
    const yearlySavings = monthlyTotal - parseFloat(yearlyPrice)
    return yearlySavings > 0 ? yearlySavings : 0
  }
  return 0
}

// Handle membership selection
const selectMembership = (membership) => {
  // Check if user is logged in
  if (!userSession.isLoggedIn) {
    // Store the current URL to redirect back after login
    const currentPath = window.location.pathname

    // Navigate to login page with redirect parameter
    navigateTo({
      path: '/auth/',
      query: {
        redirect: currentPath
      }
    })
    return
  }

  // Navigate to subscription confirmation page
  navigateTo({
    path: `/app/subscription/confirm/${spaceData.value.space.slug}/${membership.id}`
  })
}

// Intersection observer for sticky header
useIntersectionObserver(headerRef, ([{ isIntersecting }]) => {
  isHeaderSticky.value = !isIntersecting
})

// Methods
function formatPrice(amount: number) {
  return new Intl.NumberFormat('en-MA', {
    style: 'currency',
    currency: 'MAD',
    minimumFractionDigits: 0
  }).format(amount)
}

function formatDate(date: Date | null) {
  if (!date) return 'Select date'
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

function formatDateTime(date: Date | null) {
  if (!date) return 'Select date'
  
  const dateStr = new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
  
  if (bookingType.value === 'hourly') {
    const timeStr = selectedTimes.value.startTime
    return `${dateStr}, ${formatTimeLabel(timeStr)}`
  }
  
  return dateStr
}

function formatTimeLabel(time: string) {
  const [hours] = time.split(':')
  const hour = parseInt(hours)
  const period = hour >= 12 ? 'PM' : 'AM'
  const displayHour = hour % 12 || 12
  return `${displayHour}:00 ${period}`
}

function generateTimeSlots(start: number, end: number) {
  const slots = [];
  for (let hour = start; hour <= end; hour++) {
    const time = `${hour.toString().padStart(2, '0')}:00`;
    slots.push({
      label: formatTimeLabel(time),
      value: time
    });
  }
  return slots;
}

function toggleCategory(index: number) {
  if (expandedCategories.value.includes(index)) {
    expandedCategories.value = expandedCategories.value.filter(i => i !== index)
  } else {
    expandedCategories.value.push(index)
  }
}

function toggleCheckIn() {
  isCheckInOpen.value = !isCheckInOpen.value
  if (isCheckInOpen.value) {
    isCheckOutOpen.value = false
  }
}

function toggleCheckOut() {
  isCheckOutOpen.value = !isCheckOutOpen.value
  if (isCheckOutOpen.value) {
    isCheckInOpen.value = false
  }
}

function handleCheckInSelect(day: any) {
  // Get the operating hours for the selected day
  const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][day.date.getDay()];
  const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];
  
  if (!operatingHours?.isOpen) {
    console.warn('Selected day is closed');
    return;
  }

  // Parse opening hours
  const [openHour, openMinute] = operatingHours.open.split(':').map(Number);
  const [closeHour] = operatingHours.close.split(':').map(Number);

  // Create date with opening time
  const checkInDate = new Date(day.date);
  checkInDate.setHours(openHour, openMinute, 0, 0);
  selectedDates.value.checkIn = checkInDate;
  isCheckInOpen.value = false;

  if (bookingType.value === 'hourly') {
    // For hourly bookings
    selectedDates.value.checkOut = new Date(checkInDate); // Same day
    selectedTimes.value.startTime = `${String(openHour).padStart(2, '0')}:00`;
    
    // Set default end time to one hour after start
    const endHour = Math.min(openHour + 1, closeHour);
    selectedTimes.value.endTime = `${String(endHour).padStart(2, '0')}:00`;
    
    // Update the checkout date with the end time
    const checkOutDate = new Date(checkInDate);
    checkOutDate.setHours(endHour, 0, 0, 0);
    selectedDates.value.checkOut = checkOutDate;
  } else {
    // For daily bookings
    // Set check-in to opening time
    selectedDates.value.checkIn = checkInDate;
    
    // If checkout date exists but is before or equal to new check-in, reset it
    if (selectedDates.value.checkOut && selectedDates.value.checkOut <= checkInDate) {
      selectedDates.value.checkOut = null;
    }
    
    // Open checkout calendar if no checkout date is selected
    if (!selectedDates.value.checkOut) {
      isCheckOutOpen.value = true;
    }
  }
}

function handleCheckOutSelect(day: any) {
  // Get the operating hours for the selected day
  const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][day.date.getDay()];
  const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];
  
  if (!operatingHours?.isOpen) {
    console.warn('Selected day is closed');
    return;
  }

  // Parse closing hours
  const [closeHour, closeMinute] = operatingHours.close.split(':').map(Number);

  // Create date with closing time
  const checkOutDate = new Date(day.date);
  
  if (bookingType.value === 'daily') {
    // For daily bookings, set to closing time of the selected day
    checkOutDate.setHours(closeHour, closeMinute, 0, 0);
  } else {
    // For hourly bookings, use the selected end time
    const [endHour, endMinute] = selectedTimes.value.endTime.split(':').map(Number);
    checkOutDate.setHours(endHour, endMinute, 0, 0);
  }
  
  selectedDates.value.checkOut = checkOutDate;
  isCheckOutOpen.value = false;
}

// Update handleReserve function
async function handleReserve() {
  if (!isValidBooking.value) return;
  
  // Check if user is logged in
  if (!userSession.isLoggedIn) {
    // Store the current URL to redirect back after login
    const currentPath = window.location.pathname
    
    // Navigate to login page with redirect parameter
    await navigateTo({
      path: '/auth/',
      query: {
        redirect: currentPath
      }
    })
    return
  }
  
  const checkIn = formatDateForApi(
    selectedDates.value.checkIn,
    bookingType.value === 'hourly' ? selectedTimes.value.startTime : undefined
  );
  
  const checkOut = formatDateForApi(
    selectedDates.value.checkOut,
    bookingType.value === 'hourly' ? selectedTimes.value.endTime : undefined
  );
  
  // Call bookSpace function
  bookSpace();
}

// Gallery functions
function openPhotoGallery(index) {
  activeImageIndex.value = index
  showAllPhotos.value = true
  // Prevent scrolling when gallery is open
  document.body.style.overflow = 'hidden'
}

function closePhotoGallery() {
  showAllPhotos.value = false
  isZoomed.value = false
  // Restore scrolling
  document.body.style.overflow = ''
}

function navigateGallery(direction) {
  if (isTransitioning.value) return
  isTransitioning.value = true
  
  // Reset zoom state when navigating
  isZoomed.value = false
  
  // Calculate new index with wrapping
  const newIndex = (activeImageIndex.value + direction + allPhotos.value.length) % allPhotos.value.length
  activeImageIndex.value = newIndex
  
  // Reset transitioning state after animation completes
  setTimeout(() => {
    isTransitioning.value = false
  }, 300)
}

function toggleZoom() {
  isZoomed.value = !isZoomed.value
}

function handleTouchStart(event) {
  touchStartX.value = event.changedTouches[0].screenX
}

function handleTouchEnd(event) {
  touchEndX.value = event.changedTouches[0].screenX
  const diff = touchEndX.value - touchStartX.value
  
  // Swipe threshold of 50px
  if (Math.abs(diff) > 50) {
    navigateGallery(diff > 0 ? -1 : 1)
  }
}

function handleKeydown(event) {
  if (!showAllPhotos.value) return
  
  switch (event.key) {
    case 'ArrowLeft':
      navigateGallery(-1)
      break
    case 'ArrowRight':
      navigateGallery(1)
      break
    case 'Escape':
      closePhotoGallery()
      break
    case ' ':
      toggleZoom()
      event.preventDefault()
      break
  }
}

function getUtilizationColor(utilization: string) {
  const percentage = parseInt(utilization)
  return percentage < 50 ? 'low' : percentage < 80 ? 'medium' : 'high'
}

// Helper function to format operation hours
const formatOperationHours = (dayData) => {
  if (!dayData || !dayData.isOpen) return 'Closed';
  
  // Format time in 12-hour format with AM/PM
  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };
  
  return `${formatTime(dayData.open)} - ${formatTime(dayData.close)}`;
};

// Get current day of week
const getCurrentDayOfWeek = () => {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  return days[new Date().getDay()];
};

// Computed property for today's hours
const todaysHours = computed(() => {
  if (!spaceData.value?.space?.operation) return null;
  
  const currentDay = getCurrentDayOfWeek();
  const dayData = spaceData.value.space.operation[currentDay];
  
  if (!dayData || !dayData.isOpen) return 'Closed today';
  return `Open today: ${formatOperationHours(dayData)}`;
});

// Computed property to check if space is currently open
const isCurrentlyOpen = computed(() => {
  if (!spaceData.value?.space?.operation) return false;
  
  const currentDay = getCurrentDayOfWeek();
  const dayData = spaceData.value.space.operation[currentDay];
  
  if (!dayData || !dayData.isOpen) return false;
  
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  
  const [openHour, openMinute] = dayData.open.split(':').map(Number);
  const [closeHour, closeMinute] = dayData.close.split(':').map(Number);
  
  const currentTimeInMinutes = currentHour * 60 + currentMinute;
  const openTimeInMinutes = openHour * 60 + openMinute;
  const closeTimeInMinutes = closeHour * 60 + closeMinute;
  
  return currentTimeInMinutes >= openTimeInMinutes && currentTimeInMinutes < closeTimeInMinutes;
});

// Filter and sort options for reviews
const reviewFilters = ref({
  rating: 'all',
  hasPhotos: false,
  sortBy: 'recent' // 'recent', 'highest', 'lowest', 'relevant'
});

// Pagination for reviews
const reviewsPerPage = 5;
const currentReviewPage = ref(1);

// Filtered reviews based on selected filters
const filteredReviews = computed(() => {
  let filtered = reviews.value ? [...reviews.value] : [];

  // Filter by rating
  if (reviewFilters.value.rating !== 'all') {
    const ratingValue = parseInt(reviewFilters.value.rating);
    filtered = filtered.filter(review => review.rating === ratingValue);
  }

  // Filter by photos
  if (reviewFilters.value.hasPhotos) {
    filtered = filtered.filter(review => review.photos && review.photos.length > 0);
  }

  // Sort reviews
  switch (reviewFilters.value.sortBy) {
    case 'highest':
      filtered.sort((a, b) => b.rating - a.rating);
      break;
    case 'lowest':
      filtered.sort((a, b) => a.rating - b.rating);
      break;
    case 'relevant':
      filtered.sort((a, b) => b.helpful - a.helpful);
      break;
    case 'recent':
    default:
      filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
      break;
  }

  return filtered;
});

// Paginated reviews for display
const paginatedReviews = computed(() => {
  const startIndex = (currentReviewPage.value - 1) * reviewsPerPage;
  const endIndex = startIndex + reviewsPerPage;
  return filteredReviews.value.slice(startIndex, endIndex);
});

// Total pages for pagination
const totalReviewPages = computed(() => {
  return Math.ceil(filteredReviews.value.length / reviewsPerPage);
});

// Reset pagination when filters change
watch([reviewFilters], () => {
  currentReviewPage.value = 1;
}, { deep: true });

// Reply functionality
const replyingTo = ref<string | null>(null);
const replyContent = ref('');
const isSubmittingReply = ref(false);
const maxReplyLength = 500;

// Toggle reply form for a specific review
const toggleReplyForm = (reviewId: string) => {
  if (replyingTo.value === reviewId) {
    replyingTo.value = null;
    replyContent.value = '';
  } else {
    replyingTo.value = reviewId;
    replyContent.value = '';
  }
};

// Submit reply to a review
const submitReply = async (reviewId: string) => {
  if (!replyContent.value.trim() ||
      isSubmittingReply.value ||
      replyContent.value.length > maxReplyLength ||
      replyContent.value.length < 10) {
    return;
  }

  try {
    isSubmittingReply.value = true;

    // Here you would call your API to submit the reply
    // const response = await $api.review.replyToReview(reviewId, {
    //   content: replyContent.value.trim()
    // });

    console.log('Submitting reply to review:', reviewId, replyContent.value.trim());

    // Show success message
    const toaster = useToaster();
    toaster.show({
      title: 'Reply Posted!',
      message: 'Your reply has been posted successfully.',
      color: 'success',
      icon: 'ph:check-circle',
      closable: true,
    });

    // Reset form
    replyingTo.value = null;
    replyContent.value = '';

    // Optionally refresh reviews to show the new reply
    // await fetchReviews();

  } catch (err) {
    console.error('Error submitting reply:', err);
    const toaster = useToaster();
    toaster.show({
      title: 'Error',
      message: 'Failed to post reply. Please try again.',
      color: 'danger',
      icon: 'ph:warning-circle',
      closable: true,
    });
  } finally {
    isSubmittingReply.value = false;
  }
};

// Cancel reply
const cancelReply = () => {
  replyingTo.value = null;
  replyContent.value = '';
};

// Get visible page numbers for compact pagination
const getVisiblePages = () => {
  const total = totalReviewPages.value;
  const current = currentReviewPage.value;
  const pages = [];

  if (total <= 5) {
    // Show all pages if 5 or fewer
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (current > 3) {
      pages.push('...');
    }

    // Show current page and neighbors
    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);

    for (let i = start; i <= end; i++) {
      if (!pages.includes(i)) {
        pages.push(i);
      }
    }

    if (current < total - 2) {
      pages.push('...');
    }

    // Always show last page
    if (!pages.includes(total)) {
      pages.push(total);
    }
  }

  return pages;
};

// Generate realistic review timestamps and booking context
const getReviewTimestamp = (review: any) => {
  // Generate varied dates from the last 18 months
  const now = new Date();
  const monthsAgo = Math.floor(Math.random() * 18) + 1;
  const reviewDate = new Date(now.getFullYear(), now.getMonth() - monthsAgo, Math.floor(Math.random() * 28) + 1);
  return reviewDate;
};

const getBookingContext = (review: any) => {
  const reviewDate = getReviewTimestamp(review);
  const stayDate = new Date(reviewDate.getTime() - (Math.floor(Math.random() * 30) + 1) * 24 * 60 * 60 * 1000);

  const contexts = [
    `Stayed in ${format(stayDate, 'MMM yyyy')}`,
    `Coworking space • ${format(stayDate, 'MMM yyyy')}`,
    `Meeting room • ${format(stayDate, 'MMM yyyy')}`,
    `Conference room • ${format(stayDate, 'MMM yyyy')}`,
    `Event space • ${format(stayDate, 'MMM yyyy')}`,
    `Private office • ${format(stayDate, 'MMM yyyy')}`,
  ];

  return contexts[Math.floor(Math.random() * contexts.length)];
};

// Generate review insights and highlights
const getReviewHighlights = (review: any) => {
  const highlights = [];

  // Most liked review (highest helpful votes)
  if (review.helpfulVotes >= 15) {
    highlights.push({ type: 'most-liked', label: 'Most Liked Review', icon: 'ph:heart-fill', color: 'text-red-500' });
  }

  // Content-based highlights
  const content = review.content.toLowerCase();

  if (content.includes('clean') || content.includes('spotless') || content.includes('tidy')) {
    highlights.push({ type: 'cleanliness', label: 'Mentions Cleanliness', icon: 'ph:sparkle', color: 'text-blue-500' });
  }

  if (content.includes('noise') || content.includes('loud') || content.includes('quiet')) {
    highlights.push({ type: 'noise', label: 'Mentioned Noise Levels', icon: 'ph:speaker-high', color: 'text-orange-500' });
  }

  if (content.includes('wifi') || content.includes('internet') || content.includes('connection')) {
    highlights.push({ type: 'wifi', label: 'Good Wi-Fi', icon: 'ph:wifi-high', color: 'text-blue-500' });
  }

  if (content.includes('location') || content.includes('convenient') || content.includes('central')) {
    highlights.push({ type: 'location', label: 'Great Location', icon: 'ph:map-pin', color: 'text-purple-500' });
  }

  if (content.includes('staff') || content.includes('service') || content.includes('helpful')) {
    highlights.push({ type: 'service', label: 'Excellent Service', icon: 'ph:users', color: 'text-indigo-500' });
  }

  return highlights.slice(0, 2); // Limit to 2 highlights per review
};

// Generate AI summary for reviews
const generateReviewSummary = computed(() => {
  if (!filteredReviews.value.length) return '';

  const totalReviews = filteredReviews.value.length;
  const avgRating = filteredReviews.value.reduce((sum, r) => sum + r.rating, 0) / totalReviews;

  // Analyze common themes
  const allContent = filteredReviews.value.map(r => r.content.toLowerCase()).join(' ');

  const themes = {
    cleanliness: (allContent.match(/clean|spotless|tidy|dirty|messy/g) || []).length,
    location: (allContent.match(/location|convenient|central|close|nearby/g) || []).length,
    staff: (allContent.match(/staff|service|helpful|friendly|rude/g) || []).length,
    amenities: (allContent.match(/amenities|facilities|pool|gym|wifi/g) || []).length,
    noise: (allContent.match(/noise|loud|quiet|peaceful/g) || []).length,
  };

  const topThemes = Object.entries(themes)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 2)
    .map(([theme]) => theme);

  let summary = '';

  if (avgRating >= 4.5) {
    summary = 'Guests consistently praise this property. ';
  } else if (avgRating >= 4.0) {
    summary = 'Most guests had a positive experience. ';
  } else if (avgRating >= 3.5) {
    summary = 'Guests had mixed experiences. ';
  } else {
    summary = 'Reviews highlight areas for improvement. ';
  }

  if (topThemes.includes('location')) {
    summary += 'Location is frequently mentioned as a highlight. ';
  }

  if (topThemes.includes('cleanliness')) {
    summary += 'Cleanliness standards receive attention in reviews. ';
  }

  if (topThemes.includes('staff')) {
    summary += 'Service quality is commonly discussed. ';
  }

  return summary.trim();
});

// Format review date
function formatReviewDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    year: 'numeric'
  });
}

// Event helper functions
function formatEventDate(dateString) {
  const date = new Date(dateString);
  const month = date.toLocaleDateString('en-US', { month: 'short' });
  const day = date.getDate();
  return `${month} ${day}`;
}

function formatEventTime(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const startTime = start.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const endTime = end.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  return `${startTime} - ${endTime}`;
}

function getEventTypeColor(type) {
  const colors = {
    'meeting': 'primary',
    'workshop': 'success',
    'networking': 'info',
    'conference': 'warning',
    'seminar': 'primary',
    'training': 'success',
    'social': 'pink',
    'community': 'indigo',
    'private': 'muted',
    'other': 'muted'
  };
  return colors[type] || 'muted';
}

function getEventDuration(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffMs = end.getTime() - start.getTime();
  const diffMins = Math.round(diffMs / (1000 * 60));

  if (diffMins < 60) {
    return `${diffMins} min`;
  } else {
    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  }
}

// Mark review as helpful
const markingHelpful = ref(new Set());

// Report modal state
const isReportModalOpen = ref(false);
const reportingReviewId = ref<string | null>(null);
const reportForm = ref({
  reason: '',
  description: ''
});
const isSubmittingReport = ref(false);

// Claim modal state
const isClaimModalOpen = ref(false);
const isSubmittingClaim = ref(false);

// Claim form data
const claimForm = ref({
  fullName: '',
  email: '',
  phoneNumber: '',
  position: '',
  businessName: '',
  businessRegistrationNumber: '',
  message: '',
});

// Computed for form validation
const isClaimFormValid = computed(() => {
  return claimForm.value.fullName &&
         claimForm.value.email &&
         claimForm.value.phoneNumber &&
         claimForm.value.position &&
         claimForm.value.businessName &&
         claimForm.value.message;
});

// Report reasons
const reportReasons = [
  { value: 'spam', label: 'Spam or fake content', icon: 'ph:warning-circle' },
  { value: 'inappropriate', label: 'Inappropriate language', icon: 'ph:chat-circle-text' },
  { value: 'harassment', label: 'Harassment or bullying', icon: 'ph:user-minus' },
  { value: 'misinformation', label: 'False or misleading information', icon: 'ph:info' },
  { value: 'copyright', label: 'Copyright violation', icon: 'ph:copyright' },
  { value: 'other', label: 'Other (please specify)', icon: 'ph:dots-three' }
];

async function markReviewHelpful(reviewId: string) {
  if (markingHelpful.value.has(reviewId)) return;

  try {
    markingHelpful.value.add(reviewId);

    // Call the API to mark review as helpful
    const updatedReview = await $api.review.markReviewAsHelpful(reviewId);

    // Update the local review with the new helpful count
    const reviewIndex = reviews.value.findIndex(r => r.id === reviewId);
    if (reviewIndex !== -1) {
      reviews.value[reviewIndex].helpfulVotes = updatedReview.helpfulVotes;
    }

    // Show success feedback
    const toaster = useToaster();
    toaster.show({
      title: 'Thank you!',
      message: 'Your feedback has been recorded.',
      color: 'success',
      icon: 'ph:thumbs-up',
      closable: true,
    });

  } catch (err) {
    console.error('Error marking review as helpful:', err);

    // Show error feedback
    const toaster = useToaster();
    toaster.show({
      title: 'Error',
      message: 'Failed to mark review as helpful. Please try again.',
      color: 'danger',
      icon: 'ph:warning-circle',
      closable: true,
    });
  } finally {
    markingHelpful.value.delete(reviewId);
  }
}

// Open report modal
function openReportModal(reviewId: string) {
  reportingReviewId.value = reviewId;
  reportForm.value = {
    reason: '',
    description: ''
  };
  isReportModalOpen.value = true;
}

// Close report modal
function closeReportModal() {
  isReportModalOpen.value = false;
  reportingReviewId.value = null;
  reportForm.value = {
    reason: '',
    description: ''
  };
}

// Claim modal functions
function openClaimModal() {
  if (!spaceData.value?.space) {
    console.warn('Cannot open claim modal: space data not available');
    return;
  }
  isClaimModalOpen.value = true;
}

function closeClaimModal() {
  isClaimModalOpen.value = false;
  // Reset form when closing
  claimForm.value = {
    fullName: '',
    email: '',
    phoneNumber: '',
    position: '',
    businessName: '',
    businessRegistrationNumber: '',
    message: '',
  };
}

async function submitClaimRequest() {
  if (!isClaimFormValid.value || !spaceData.value?.space) {
    return;
  }

  isSubmittingClaim.value = true;

  try {
    const response = await $api.space.claimSpace(spaceData.value.space.id, claimForm.value);

    console.log('Claim submitted successfully:', response);

    // Show success message (you can add toast notification here)
    alert('Claim request submitted successfully! We will review it and get back to you within 2-3 business days.');

    closeClaimModal();
  } catch (error: any) {
    console.error('Error submitting claim:', error);
    alert('Failed to submit claim request. Please try again.');
  } finally {
    isSubmittingClaim.value = false;
  }
}

// Submit report
async function submitReport() {
  if (!reportingReviewId.value || !reportForm.value.reason) return;

  try {
    isSubmittingReport.value = true;

    // Here you would call the API to report the review
    // await $api.review.reportReview(reportingReviewId.value, reportForm.value);

    // For now, just simulate the API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Show success feedback
    const toaster = useToaster();
    toaster.show({
      title: 'Report Submitted',
      message: 'Thank you for your report. We will review it shortly.',
      color: 'success',
      icon: 'ph:check-circle',
      closable: true,
    });

    closeReportModal();

  } catch (err) {
    console.error('Error submitting report:', err);

    // Show error feedback
    const toaster = useToaster();
    toaster.show({
      title: 'Error',
      message: 'Failed to submit report. Please try again.',
      color: 'danger',
      icon: 'ph:warning-circle',
      closable: true,
    });
  } finally {
    isSubmittingReport.value = false;
  }
}

// Helper function to format date for API
function formatDateForApi(date: Date | null, time?: string): string | null {
  if (!date) return null;
  
  const formattedDate = new Date(date);
  
  if (time) {
    // For hourly bookings, set the specific time
    const [hours, minutes] = time.split(':').map(Number);
    formattedDate.setHours(hours, minutes || 0, 0, 0);
  } else {
    // For daily bookings, use the operating hours
    const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][formattedDate.getDay()];
    const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];
    
    if (operatingHours?.isOpen) {
      if (date === selectedDates.value.checkIn) {
        // Set check-in to opening time
        const [openHour, openMinute] = operatingHours.open.split(':').map(Number);
        formattedDate.setHours(openHour, openMinute || 0, 0, 0);
      } else if (date === selectedDates.value.checkOut) {
        // Set check-out to closing time
        const [closeHour, closeMinute] = operatingHours.close.split(':').map(Number);
        formattedDate.setHours(closeHour, closeMinute || 0, 0, 0);
      }
    }
  }
  
  return formattedDate.toISOString();
}

// Add a watch effect for time changes
watch(
  [
    () => selectedTimes.value?.startTime,
    () => selectedTimes.value?.endTime
  ],
  ([newStartTime, newEndTime]) => {
    if (bookingType.value === 'hourly' && newStartTime && newEndTime) {
      const [startHour] = newStartTime.split(':').map(Number);
      const [endHour] = newEndTime.split(':').map(Number);

      // If start time is after or equal to end time, reset end time
      if (startHour >= endHour) {
        // Set end time to the next available hour after start time
        const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][selectedDates.value?.checkIn?.getDay() ?? 0];
        const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];
        
        if (operatingHours?.isOpen) {
          const [closeHour] = operatingHours.close.split(':').map(Number);
          const newEndHour = Math.min(startHour + 1, closeHour);
          
          if (newEndHour > startHour) {
            selectedTimes.value.endTime = `${String(newEndHour).padStart(2, '0')}:00`;
            
            // Update checkout date with new end time
            if (selectedDates.value?.checkIn) {
              const checkOutDate = new Date(selectedDates.value.checkIn);
              checkOutDate.setHours(newEndHour, 0, 0, 0);
              selectedDates.value.checkOut = checkOutDate;
            }
          }
        }
      }
    }
  }
);

// Update the watcher to use the debounced function
watch(
  [
    () => selectedDates.value?.checkIn,
    () => selectedDates.value?.checkOut,
    () => selectedTimes.value?.startTime,
    () => selectedTimes.value?.endTime,
    () => guestCount.value,
    () => selectedRoom.value,
    () => bookingType.value
  ],
  async () => {
    // Reset availability when parameters change
    isAvailable.value = false;
    
    // Check availability if all required fields are filled
    if (hasSelectedDates.value && 
        guestCount.value > 0 && 
        selectedRoom.value) {
      await debouncedCheckAvailability();
    }
  }
);

// Helper function to check if a date is today
function isDateToday(date) {
  if (!date) return false;
  const today = new Date();
  return date.getDate() === today.getDate() &&
         date.getMonth() === today.getMonth() &&
         date.getFullYear() === today.getFullYear();
}

// Helper function to format time in 12-hour format
function formatTime(time) {
  const [hours] = time.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12;
  return `${displayHours}:00 ${period}`;
}

// Update the watch effect for dates to handle today's date
watch(
  () => selectedDates.value.checkIn,
  (newDate) => {
    if (newDate) {
      // Reset times when date changes
      selectedTimes.value = {
        startTime: '',
        endTime: ''
      };

      // If it's today and current time is after closing time, show a message
      if (isDateToday(newDate)) {
        const currentHour = new Date().getHours();
        const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][newDate.getDay()];
        const operatingHours = spaceData.value?.space?.operation?.[dayOfWeek];

        if (operatingHours?.isOpen) {
          const [closeHour] = operatingHours.close.split(':').map(Number);
          if (currentHour >= closeHour) {
            // Reset the date as it's past closing time
            selectedDates.value.checkIn = null;
            // Show error message (you can implement this based on your UI needs)
            console.warn('Selected time is after closing hours');
          }
        }
      }
    }
  }
);

const updateActiveSection = () => {
  if (!$el.value) return; // Add early return if $el is not available
  
  const element = $el.value.closest('[data-scroll-container]');
  if (!element) return; // Add early return if container is not found

  const sections = Array.from(element.querySelectorAll('[data-section]'));
  // ... rest of the function
}

// Add these computed properties/refs
const spaceHighlights = [
  { title: 'Recently renovated', icon: 'ph:paint-brush-duotone' },
  { title: 'Professional cleaning', icon: 'ph:sparkle-duotone' },
  { title: 'High-speed internet', icon: 'ph:wifi-high-duotone' },
  { title: 'Meeting rooms included', icon: 'ph:users-three-duotone' }
]

const allowedRules = [
  'Suitable for quiet work',
  'Video calls allowed',
  'Kitchen access',
  'Pet friendly'
]

const notAllowedRules = [
  'No smoking',
  'No events/parties',
  'No loud music',
  'No outside food delivery'
]

// Add this computed property
const totalCapacity = computed(() => {
  if (!spaceData.value?.space?.rooms?.length) return 0;
  
  return spaceData.value.space.rooms.reduce((total, room) => {
    return total + (room.capacity || 0);
  }, 0);
});

const toaster = useToaster()

// Add toggle bookmark function
const toggleBookmark = async () => {
  
  try {
    isBookmarkLoading.value = true;
    const response = await $api.bookmark.toggleBookmark(spaceData.value.space.id);
    isBookmarked.value = response.isBookmarked;
    toaster.clearAll()
    if (isBookmarked.value) {
      toaster.show({
        title: 'You Hearted This Space!',
        message: `It's now in your bookmarks!`,
        color: 'success',
        icon: 'ph:heart-fill',
        closable: true,

      })
    } else {
      toaster.show({
        title: 'Alright!',
        message: `Seemed like you changed your mind.`,
        color: 'success',
        icon: 'ph:heart',
        closable: true,
      })
    }
  } catch (err) {
    console.error('Error toggling bookmark:', err);
  } finally {
    isBookmarkLoading.value = false;
  }
};

// House Rules
const houseRules = ref([
  'Quiet Environment',
  'Video Calls Allowed',
  'Kitchen Access',
  'Pet Friendly'
]);

// Additional Guidelines
const additionalGuidelines = ref([
  'Guest Policy: Guests must be registered at reception. Maximum of 2 guests per member at a time.',
  'Meeting Room Usage: Please book meeting rooms in advance. Release your booking if you no longer need the space.',
  'Professional Conduct: Please treat all members and staff with respect. Harassment of any kind will not be tolerated.'
]);

// Membership Options
const membershipOptions = ref([
  {
    name: 'Monthly Membership',
    price: 1200,
    benefits: [
      'Unlimited access during operating hours',
      'High-speed internet access',
      '5 hours of meeting room credits/month',
      'Complimentary coffee and tea',
      'Access to community events'
    ]
  },
  {
    name: 'Annual Membership',
    price: 11000,
    benefits: [
      'All benefits of monthly membership',
      '10 hours of meeting room credits/month',
      'Dedicated desk option available',
      'Personal locker included',
      'Priority booking for meeting rooms'
    ]
  }
]);

// Additional Membership Benefits
const additionalBenefits = ref([
  'Networking opportunities with other professionals',
  'Access to professional workshops and events',
  'Member-only discounts on additional services'
]);

// Quote form state
const quoteForm = reactive({
  roomId: '',
  numberOfPeople: 1,
  startDate: null,
  endDate: null,
  price: null,
  currency: 'MAD',
  contactInfo: '',
  notes: '',
  contactInfo: '',
  guestFirstName: '',
  guestLastName: '',
  guestEmail: '',
  termsAccepted: false,
  isSubmitting: false,
  error: null,
  success: null,
  status: QuoteStatus.REQUESTED
});

// Quote form validation
const isValidQuoteForm = computed(() => {
  const basicValidation = quoteForm.roomId && quoteForm.numberOfPeople && quoteForm.startDate && quoteForm.endDate && quoteForm.termsAccepted;

  // If user is logged in, no additional validation needed
  if (userSession?.user?.id) {
    return basicValidation;
  }

  // If user is not logged in (guest), require guest contact information
  return basicValidation && quoteForm.guestFirstName && quoteForm.guestLastName && quoteForm.guestEmail;
});

// Handle quote form submission
const submitQuoteRequest = async () => {
  quoteForm.isSubmitting = true;
  quoteForm.error = null;
  quoteForm.success = null;

  try {
    if (!spaceData.value?.space?.id) {
      throw new Error('Space data not available');
    }

    // Prepare payload that matches the backend CreateQuoteDto structure
    const payload = {
      spaceId: spaceData.value.space.id,
      roomId: quoteForm.roomId,
      userId: userSession?.user?.id,
      numberOfPeople: quoteForm.numberOfPeople,
      price: quoteForm.price,
      currency: quoteForm.currency,
      startDate: quoteForm.startDate,
      endDate: quoteForm.endDate,
      status: quoteForm.status,
      notes: quoteForm.notes,
      contactInfo: quoteForm.contactInfo,
      // Include guest information if user is not logged in
      guestFirstName: !userSession?.user?.id ? quoteForm.guestFirstName : undefined,
      guestLastName: !userSession?.user?.id ? quoteForm.guestLastName : undefined,
      guestEmail: !userSession?.user?.id ? quoteForm.guestEmail : undefined,
    };

    // Call the API to create a quote request
    await $api.quote.createQuote(payload);
    quoteForm.success = 'Your quote request has been submitted successfully!';

    // Reset form after successful submission
    setTimeout(() => {
      quoteForm.roomId = '';
      quoteForm.numberOfPeople = 1;
      quoteForm.startDate = null;
      quoteForm.endDate = null;
      quoteForm.period = null;
      quoteForm.price = null;
      quoteForm.notes = '';
      quoteForm.contactInfo = '';
      quoteForm.guestFirstName = '';
      quoteForm.guestLastName = '';
      quoteForm.guestEmail = '';
      quoteForm.termsAccepted = false;
      quoteForm.success = null;
    }, 5000);
  } catch (err) {
    console.error('Error submitting quote request:', err);
    quoteForm.error = err instanceof Error ? err.message : 'An error occurred while submitting your quote request';
  } finally {
    quoteForm.isSubmitting = false;
  }
};

// Handle quote start date selection
const handleQuoteStartDateSelect = (day: any) => {
  quoteForm.startDate = day.date;
  isQuoteStartDateOpen.value = false;
  
  // Reset end date if it's before start date
  if (quoteForm.endDate && quoteForm.endDate < day.date) {
    quoteForm.endDate = null;
  }
};

// Handle quote end date selection
const handleQuoteEndDateSelect = (day: any) => {
  quoteForm.endDate = day.date;
  isQuoteEndDateOpen.value = false;
};

// Quote form state
const isQuoteStartDateOpen = ref(false);
const isQuoteEndDateOpen = ref(false);
const isTourDateOpen = ref(false);
const isSubmitting = ref(false); // Add this line

// Tour form data
const tourForm = ref({
  date: '',
  time: '',
  numberOfPeople: 1,
  duration: 60,
  phoneNumber: '',
  email: '',
  purpose: '',
  preferredContactMethod: 'email',
  tourType: 'in_person',
  specialRequirements: '',
  spaceId: '',
  roomId: '',
  termsAccepted: false
})

// Add this to the script section
const submitTour = async () => {
  try {
    isSubmitting.value = true
    tourForm.value.spaceId = spaceData.value?.space?.id
    const response = await $api.tour.createTour(tourForm.value)
    console.log('Tour scheduled successfully:', response)
    // Show success message
    useToast().add({
      title: 'Success',
      description: 'Your tour has been scheduled successfully.',
      color: 'success',
      icon: 'lucide:check',
    })
  } catch (error) {
    console.error('Error scheduling tour:', error)
    // Show error message
    useToast().add({
      title: 'Error',
      description: 'There was an error scheduling your tour. Please try again.',
      color: 'danger',
      icon: 'lucide:alert-circle',
    })
  } finally {
    isSubmitting.value = false
  }
}

// Tour-specific date handler
const handleTourDateSelect = (day: any) => {
  tourForm.value.date = day.date;
  isTourDateOpen.value = false;
};

const url = computed(() => {
  return window.location.href
})
</script>

<template>
  <div>
    <!-- Loading state -->
    <div v-if="pending" class="flex items-center justify-center min-h-[60vh]">
      <div class="animate-pulse flex flex-col items-center">
        <div class="size-16 rounded-full bg-muted-200 dark:bg-muted-700 mb-4"></div>
        <div class="h-4 w-32 bg-muted-200 dark:bg-muted-700 rounded-md"></div>
        <div class="h-3 w-48 bg-muted-200 dark:bg-muted-700 rounded-md mt-3"></div>
      </div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="flex items-center justify-center min-h-[60vh]">
      <div class="text-center">
        <Icon name="ph:warning-circle" class="size-16 text-danger-500 mb-4" />
        <h2 class="text-xl font-bold mb-2">Failed to load workspace data</h2>
        <p class="text-muted-500 mb-4">{{ error.message || 'Please try again later' }}</p>
        <BaseButton @click="fetchSpace">Retry</BaseButton>
      </div>
    </div>

    <!-- Content when data is available -->
    <div v-else-if="spaceData?.space">

      <!-- Photo Gallery Modal -->
      <Teleport to="body">
        <Transition name="modal">
          <div 
            v-if="showAllPhotos" 
            class="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-xl"
            @click="closePhotoGallery"
          >
            <!-- Gallery Controls -->
            <div class="absolute top-4 right-4 z-20 flex items-center gap-4">
              <div class="bg-black/40 backdrop-blur-md px-3 py-1.5 rounded-full text-white text-sm font-medium">
                {{ activeImageIndex + 1 }} / {{ allPhotos.length }}
              </div>
              <button 
                class="size-10 rounded-full bg-black/40 backdrop-blur-md text-white flex items-center justify-center hover:bg-black/60 transition-colors"
                @click.stop="closePhotoGallery"
              >
                <Icon name="ph:x" class="size-5" />
              </button>
            </div>
            
            <!-- Navigation Buttons -->
            <button 
              v-if="allPhotos.length > 1" 
              class="absolute left-4 top-1/2 -translate-y-1/2 size-12 rounded-full bg-black/40 backdrop-blur-md text-white flex items-center justify-center hover:bg-black/60 transition-colors z-20"
              @click.stop="navigateGallery(-1)"
              :disabled="isTransitioning"
            >
              <Icon name="ph:caret-left-bold" class="size-5" />
            </button>
            
            <button 
              v-if="allPhotos.length > 1" 
              class="absolute right-4 top-1/2 -translate-y-1/2 size-12 rounded-full bg-black/40 backdrop-blur-md text-white flex items-center justify-center hover:bg-black/60 transition-colors z-20"
              @click.stop="navigateGallery(1)"
              :disabled="isTransitioning"
            >
              <Icon name="ph:caret-right-bold" class="size-5" />
            </button>
            
            <!-- Image Container -->
            <div 
              class="relative w-full h-full flex items-center justify-center z-10 overflow-hidden"
              @click.stop="toggleZoom"
              @touchstart="handleTouchStart"
              @touchend="handleTouchEnd"
            >
              <Transition name="fade" mode="out-in">
                <img 
                  :key="activeImageIndex"
                  :src="allPhotos[activeImageIndex]" 
                  :alt="`${spaceData.space.name} image ${activeImageIndex + 1}`"
                  class="max-w-full max-h-full object-contain transition-transform duration-300"
                  :class="{ 'cursor-zoom-in': !isZoomed, 'cursor-zoom-out scale-150': isZoomed }"
                  @error="(e) => { console.error('Image failed to load:', allPhotos[activeImageIndex]); e.target.src = '/img/placeholder.png'; }"
                >
              </Transition>
              
              <!-- Usage instructions -->
              <div class="absolute bottom-6 left-1/2 -translate-x-1/2 bg-black/40 backdrop-blur-md px-3 py-1.5 rounded-full text-white text-xs flex items-center gap-2 pointer-events-none opacity-70">
                <Icon name="ph:arrows-horizontal" class="size-3" />
                <span>Swipe or use arrow keys</span>
                <span class="size-1 rounded-full bg-white/50"></span>
                <Icon name="ph:magnifying-glass-plus" class="size-3" />
                <span>Tap to zoom</span>
              </div>
            </div>
          </div>
        </Transition>
      </Teleport>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <!-- Main Header -->
        <div class="pt-8 pb-6" ref="headerRef">
          <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <div class="flex items-center gap-3 mb-2">
                <div class="px-3 py-1 bg-primary-50 dark:bg-primary-500/10 rounded-full text-xs font-medium text-primary-600 dark:text-primary-400">
                  {{ spaceData.space.spaceSize === 'full_building' ? 'Full Building' : 'Partial Space' }}
                </div>
                <div 
                  class="px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1.5"
                  :class="isCurrentlyOpen 
                    ? 'bg-success-50 dark:bg-success-500/10 text-success-600 dark:text-success-400'
                    : 'bg-danger-50 dark:bg-danger-500/10 text-danger-600 dark:text-danger-400'"
                >
                  <Icon :name="isCurrentlyOpen ? 'solar:check-circle-bold-duotone' : 'solar:close-circle-bold-duotone'" class="size-3.5" />
                  <span>{{ isCurrentlyOpen ? 'Available Now' : 'Currently Closed' }}</span>
                </div>
              </div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                {{ spaceData.space.name }} 
              </h1>
              <div class="flex flex-wrap items-center gap-3 mt-3">
                <div class="flex items-center gap-1.5">
                  <Icon name="solar:map-point-bold-duotone" class="size-4 text-primary-500" />
                  <span>{{ spaceData.space.address }}</span>
                </div>
                <span class="size-1.5 rounded-full bg-muted-300 dark:bg-muted-700"></span>
                <div class="flex items-center gap-1.5">
                  <Icon name="solar:point-on-map-line-duotone" class="size-4 text-muted-500" />
                  <span>{{ spaceData.space.city.name }}</span>
                </div>
                <span class="size-1.5 rounded-full bg-muted-300 dark:bg-muted-700"></span>
                <div class="flex items-center gap-1.5">
                  <Icon name="solar:star-line-duotone" class="size-4 text-amber-500" />
                  <span>{{ spaceData.space.rating }} · {{ spaceData.space.totalReviews }} reviews</span>
                </div>
              </div>
            </div>
            <div class="flex items-center gap-4 mt-4 md:mt-0">
              <div class="flex items-center gap-3">
                <a 
                  v-if="spaceData.space.website"
                  :href="spaceData.space.website" 
                  target="_blank" 
                  class="flex items-center justify-center size-10 rounded-full bg-muted-100 dark:bg-muted-800 hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
                  title="Visit Website"
                >
                  <Icon name="solar:global-line-duotone" class="size-5" />
                </a>
                <a 
                  v-if="spaceData.space.facebook"
                  :href="spaceData.space.facebook" 
                  target="_blank" 
                  class="flex items-center justify-center size-10 rounded-full bg-muted-100 dark:bg-muted-800 hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
                  title="Visit Facebook"
                >
                  <Icon name="ph:facebook-logo" class="size-5" />
                </a>
                <a 
                  v-if="spaceData.space.instagram"
                  :href="spaceData.space.instagram" 
                  target="_blank" 
                  class="flex items-center justify-center size-10 rounded-full bg-muted-100 dark:bg-muted-800 hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
                  title="Visit Instagram"
                >
                  <Icon name="ph:instagram-logo" class="size-5" />
                </a>
                <button 
                  class="flex items-center justify-center size-10 rounded-full bg-muted-100 dark:bg-muted-800 hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
                  title="Share"
                  @click="openModal"
                >
                  <Icon name="solar:share-line-duotone" class="size-5" />
                </button>
                <button 
                  class="flex items-center justify-center size-10 rounded-full bg-muted-100 dark:bg-muted-800 hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors relative"
                  :title="isBookmarked ? 'Remove from bookmarks' : 'Save to bookmarks'"
                  @click="toggleBookmark"
                  :disabled="isBookmarkLoading"
                >
                  <Icon 
                    :name="isBookmarked ? 'solar:heart-angle-bold' : 'solar:heart-angle-line-duotone'" 
                    class="size-5" 
                    :class="{'text-danger-500': isBookmarked}" 
                  />
                  <!-- Loading indicator -->
                  <div v-if="isBookmarkLoading" class="absolute inset-0 flex items-center justify-center">
                    <div class="size-5 border-2 border-danger-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Unclaimed Space Banner - Full Width -->
        <div v-if="spaceData?.space && !spaceData.space.isClaimed" class="mb-6 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/30 rounded-lg">
          <div class="px-4 py-3 flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="flex items-center justify-center size-7 rounded-lg bg-amber-100 dark:bg-amber-800/50">
                <Icon name="ph:warning-duotone" class="size-4 text-amber-600 dark:text-amber-400" />
              </div>
              <div>
                <p class="text-sm font-medium text-amber-900 dark:text-amber-100">
                  This space is unclaimed - Booking not available
                </p>
                <p class="text-xs text-amber-700 dark:text-amber-300">
                  Contact the space directly or help the owner claim this listing to enable online booking
                </p>
              </div>
            </div>

            <div class="flex items-center gap-2">
              <BaseButton
                size="sm"
                variant="ghost"
                color="default"
                class="text-xs px-3 py-1.5 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-800/50"
              >
                <div class="flex items-center gap-1.5">
                  <Icon name="ph:phone-duotone" class="size-3.5" />
                  <span>Contact Direct</span>
                </div>
              </BaseButton>
              <BaseButton
                size="sm"
                variant="ghost"
                color="default"
                class="text-xs px-3 py-1.5 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-800/50"
                @click="openClaimModal"
              >
                <div class="flex items-center gap-1.5">
                  <Icon name="ph:arrow-square-out-duotone" class="size-3.5" />
                  <span>Claim Space</span>
                </div>
              </BaseButton>
            </div>
          </div>
        </div>

        <!-- Image Gallery -->
        <div class="relative group mb-8">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-3 rounded-2xl overflow-hidden aspect-[16/9] md:aspect-[20/9]">
            <div class="md:col-span-2 md:row-span-2 relative overflow-hidden rounded-2xl shadow-lg transform transition-transform duration-500 hover:z-10 hover:scale-[1.01]">
              <img 
                v-if="spaceData.space.mainImage" 
                :src="spaceData.space.mainImage" 
                :alt="spaceData.space.name"
                class="h-full w-full object-cover transition duration-700 group-hover:scale-[1.03] filter hover:brightness-105"
                @click="openPhotoGallery(0)"
                loading="eager"
              >
              <div v-else class="h-full w-full bg-muted-200 dark:bg-muted-800 flex items-center justify-center">
                <Icon name="ph:image" class="size-16 text-muted-400" />
              </div>
              <div class="absolute inset-0 bg-gradient-to-b from-black/10 to-black/40 transition-opacity duration-300 opacity-80 hover:opacity-60"></div>
              <div class="absolute bottom-4 left-4 text-white text-sm font-medium px-3 py-1 bg-black/60 rounded-lg backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300">Main Photo</div>
            </div>
            <div 
              v-for="(image, index) in (spaceData.space.gallery || []).slice(0, 3)" 
              :key="index" 
              class="relative overflow-hidden rounded-2xl hidden md:block shadow-md transform transition-all duration-500 hover:z-10 hover:scale-[1.03]"
            >
              <img 
                :src="image" 
                :alt="`${spaceData.space.name} image ${index + 1}`"
                class="h-full w-full object-cover transition duration-700 hover:scale-[1.05] filter hover:brightness-105"
                @click="openPhotoGallery(index + 1)"
                @error="(e) => { console.error('Image failed to load:', image); e.target.src = '/img/placeholder.png'; }"
                loading="lazy"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent transition-opacity duration-300 opacity-0 hover:opacity-100"></div>
            </div>
          </div>
          <button 
            v-if="allPhotos.length > 0"
            class="absolute bottom-5 right-5 bg-white/90 dark:bg-muted-800/90 hover:bg-white 
            dark:hover:bg-muted-700 px-4 py-2 rounded-xl text-sm font-medium shadow-xl backdrop-blur-sm transition-all duration-300 
            opacity-0 scale-95 group-hover:opacity-100 group-hover:scale-100 flex items-center gap-2"
            @click="openPhotoGallery(0)"
          >
            <Icon name="ph:camera-duotone" class="size-4" />
            View all photos
          </button>
          <div class="absolute -inset-1 -z-10 bg-gradient-to-br from-primary-500/20 via-transparent to-transparent opacity-0 blur-xl group-hover:opacity-100 transition-opacity duration-700"></div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Left Column - Space Details -->
          <div class="lg:col-span-2">
            <!-- Tabs Navigation -->
            <div class="mb-8 border-b border-muted-200 dark:border-muted-800">
              <div class="flex space-x-8">
                <button
                  v-for="tab in ['overview', 'amenities', 'rooms', 'events', 'location', 'reviews', 'quote', 'tour']"
                  :key="tab"
                  class="px-1 py-4 text-sm font-medium border-b-2 transition-colors"
                  :class="activeTab === tab ? 
                    'border-primary-500 text-primary-600 dark:text-primary-400' : 
                    'border-transparent text-muted-500 hover:text-muted-800 dark:hover:text-muted-200 hover:border-muted-300 dark:hover:border-muted-700'"
                  @click="activeTab = tab"
                >
                  {{ tab.charAt(0).toUpperCase() + tab.slice(1) }}
                </button>
              </div>
            </div>






            <!-- Tab Content -->
            <div v-if="activeTab === 'overview'" class="space-y-8">





<!-- About this space -->
              <BaseCard rounded="lg" class="mb-8">
                <div class="p-6 space-y-6 relative overflow-hidden">
                  <!-- Background Pattern -->
                  <div class="absolute -right-16 -top-16 opacity-5 dark:opacity-[0.03] pointer-events-none">
                    <Icon name="ph:buildings" class="size-72 text-primary-500" />
                  </div>
                  
                  <!-- Clean Header -->
                  <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-6 mb-6 border-b border-muted-200 dark:border-muted-800 relative">
                    <div class="flex items-center gap-4">
                      <!-- Logo Avatar instead of icon -->
                      <BaseAvatar 
                        :src="spaceData.space.logo?.path" 
                        :text="spaceData.space.name?.charAt(0) || 'S'"
                        size="lg"
                        rounded="lg"
                        class="bg-primary-500/10 dark:bg-primary-500/20 text-primary-500 font-semibold shadow-sm"
                        :classes="{
                          fallback: 'bg-primary-500/10 dark:bg-primary-500/20 text-primary-500 font-semibold'
                        }"
                      />
                      <div>
                        <h3 class="text-lg font-semibold">{{ spaceData.space.name }}</h3>
                        <div class="flex items-center gap-4 mt-1">
                          <div class="flex items-center gap-1.5 text-sm text-muted-500">
                            <Icon name="ph:users-duotone" class="size-4" />
                            <span>{{ totalCapacity }} capacity</span>
                          </div>
                          <div class="hidden sm:block size-1 rounded-full bg-muted-200 dark:bg-muted-700"></div>
                          <div class="flex items-center gap-1.5 text-sm text-muted-500">
                            <Icon name="ph:door-duotone" class="size-4" />
                            <span>{{ spaceData.space.rooms?.length || '0' }} rooms</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div class="sm:pl-4 sm:ml-4 sm:border-l border-muted-200 dark:border-muted-800">
                      <div class="flex items-center gap-1.5 text-sm">
                        <Icon name="ph:map-pin-duotone" class="size-4 text-primary-500" />
                        <span class="text-muted-500">{{ spaceData.space.address }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Description -->
                  <div class="prose prose-sm dark:prose-invert max-w-none relative">
                    <p class="text-muted-600 dark:text-muted-400 leading-relaxed text-base">
                      {{ spaceData.space.description }}
                    </p>
                  </div>

                  <!-- Key Features Grid -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 relative">
                    <!-- Space Type -->
                    <BaseCard rounded="lg" class="!bg-muted-50 dark:!bg-muted-800/50 !border-0 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                      <div class="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-transparent opacity-80 group-hover:opacity-100 transition-opacity"></div>
                      <div class="p-4 flex items-start gap-3 relative">
                        <div class="size-10 rounded-xl bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center shrink-0 
                                    shadow-sm transition-all duration-300 group-hover:scale-110 group-hover:rotate-3 group-hover:shadow-md">
                          <Icon name="ph:buildings-duotone" class="size-5 text-primary-500 group-hover:text-primary-600 transition-colors" />
                        </div>
                        <div>
                          <h4 class="font-medium mb-1">Space Type</h4>
                          <p class="text-sm text-muted-500 group-hover:text-muted-700 dark:group-hover:text-muted-300 transition-colors">
                            {{ spaceData.space.spaceSize === 'full_building' ? 'Full building access' : 'Private area within building' }}
                          </p>
                        </div>
                      </div>
                    </BaseCard>

                    <!-- Access Hours -->
                    <BaseCard rounded="lg" class="!bg-muted-50 dark:!bg-muted-800/50 !border-0 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                      <div class="absolute inset-0 bg-gradient-to-br from-success-500/5 to-transparent opacity-80 group-hover:opacity-100 transition-opacity"></div>
                      <div class="p-4 flex items-start gap-3 relative">
                        <div class="size-10 rounded-xl bg-success-50 dark:bg-success-500/10 flex items-center justify-center shrink-0
                                    shadow-sm transition-all duration-300 group-hover:scale-110 group-hover:rotate-3 group-hover:shadow-md">
                          <Icon name="ph:clock-countdown-duotone" class="size-5 text-success-500 group-hover:text-success-600 transition-colors" />
                        </div>
                        <div>
                          <h4 class="font-medium mb-1">Access Hours</h4>
                          <p class="text-sm text-muted-500 group-hover:text-muted-700 dark:group-hover:text-muted-300 transition-colors">
                            {{ spaceData.space.access === '24_7' ? '24/7 access available' : 'Business hours access' }}
                          </p>
                        </div>
                      </div>
                    </BaseCard>

                    <!-- Minimum Term -->
                    <BaseCard rounded="lg" class="!bg-muted-50 dark:!bg-muted-800/50 !border-0 relative overflow-hidden group hover:shadow-md transition-all duration-300">
                      <div class="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-transparent opacity-80 group-hover:opacity-100 transition-opacity"></div>
                      <div class="p-4 flex items-start gap-3 relative">
                        <div class="size-10 rounded-xl bg-amber-50 dark:bg-amber-500/10 flex items-center justify-center shrink-0
                                    shadow-sm transition-all duration-300 group-hover:scale-110 group-hover:rotate-3 group-hover:shadow-md">
                          <Icon name="ph:calendar-check-duotone" class="size-5 text-amber-500 group-hover:text-amber-600 transition-colors" />
                        </div>
                        <div>
                          <h4 class="font-medium mb-1">Minimum Term</h4>
                          <p class="text-sm text-muted-500 group-hover:text-muted-700 dark:group-hover:text-muted-300 transition-colors">
                            {{ spaceData.space.minimumTerm || 'No minimum term required' }}
                          </p>
                        </div>
                      </div>
                    </BaseCard>
                  </div>

                  <!-- Space Highlights -->
                  <div class="pt-6 border-t border-muted-200 dark:border-muted-800 relative">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="font-medium flex items-center gap-2">
                        <div class="flex items-center justify-center size-6 rounded-full bg-primary-50 dark:bg-primary-500/10">
                          <Icon name="ph:sparkle-duotone" class="size-4 text-primary-500" />
                        </div>
                        <span>Space Highlights</span>
                      </h4>
                      <BaseButton 
                        variant="ghost" 
                        color="primary" 
                        class="text-xs"
                        @click="() => {
                          activeTab = 'amenities';
                          // Smooth scroll to tabs
                          const tabsElement = document.querySelector('[data-section=tabs]');
                          if (tabsElement) {
                            tabsElement.scrollIntoView({ behavior: 'smooth' });
                          }
                        }"
                      >
                        <div class="flex items-center gap-2">
                          View all features
                          <Icon name="ph:arrow-right" class="size-3.5" />
                        </div>
                      </BaseButton>
                    </div>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-3">
                      <div v-for="highlight in spaceHighlights" :key="highlight.title" 
                           class="group relative p-3 rounded-xl bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 shadow-sm
                                  hover:border-primary-500/50 hover:shadow-md transition-all duration-300">
                        <div class="flex items-center gap-2">
                          <div class="size-8 rounded-lg bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center shrink-0
                                      group-hover:bg-primary-500 dark:group-hover:bg-primary-500 transition-colors duration-300">
                            <Icon 
                              :name="highlight.icon" 
                              class="size-4 text-primary-500 group-hover:text-white dark:group-hover:text-white transition-colors duration-300" 
                            />
                          </div>
                          <span class="text-sm font-medium group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">{{ highlight.title }}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Additional Info -->
                  <div class="pt-6 border-t border-muted-200 dark:border-muted-800 relative">
                    <BaseCard rounded="lg" class="!bg-gradient-to-r !from-amber-50 !to-amber-100 dark:!from-amber-500/10 dark:!to-amber-500/20 !border-amber-200 dark:!border-amber-500/20 overflow-hidden relative">
                      <div class="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4 opacity-10">
                        <Icon name="ph:info" class="size-24 text-amber-500" />
                      </div>
                      <div class="p-4 flex items-start gap-3 relative">
                        <div class="size-10 rounded-xl bg-white dark:bg-amber-500/20 flex items-center justify-center shrink-0 shadow-sm">
                          <Icon name="ph:info-duotone" class="size-5 text-amber-500" />
                        </div>
                        <div>
                          <h4 class="font-medium mb-1">Good to know</h4>
                          <p class="text-sm text-amber-700 dark:text-amber-300">
                            This space is popular among professionals and typically has high occupancy rates. 
                            We recommend booking in advance to secure your preferred dates.
                          </p>
                        </div>
                      </div>
                    </BaseCard>
                  </div>
                </div>
              </BaseCard>

              <!-- Operating Hours -->
              <BaseCard color="default" rounded="lg" class="mb-8">
                <div class="p-6 relative overflow-hidden">
                  <!-- Background Pattern -->
                  <div class="absolute -right-10 -top-10 opacity-5 dark:opacity-[0.03]">
                    <Icon name="ph:clock-clockwise" class="size-48 text-primary-500" />
                  </div>
                  
                  <div class="flex items-center justify-between mb-6 relative">
                    <div class="flex items-center gap-3">
                      <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/20 flex items-center justify-center">
                        <Icon name="ph:clock-duotone" class="size-5 text-primary-500" />
                      </div>
                      <h3 class="text-lg font-semibold">Operating Hours</h3>
                    </div>
                    <div class="flex items-center gap-2 px-3 py-1.5 rounded-full shadow-sm" 
                         :class="isCurrentlyOpen ? 
                                'bg-success-50 dark:bg-success-500/20 text-success-600 dark:text-success-400 border border-success-100 dark:border-success-500/30' : 
                                'bg-danger-50 dark:bg-danger-500/20 text-danger-600 dark:text-danger-400 border border-danger-100 dark:border-danger-500/30'"
                    >
                      <div class="size-2 rounded-full animate-pulse" :class="isCurrentlyOpen ? 'bg-success-500' : 'bg-danger-500'"></div>
                      <span class="text-sm font-medium">{{ isCurrentlyOpen ? 'Open now' : 'Closed now' }}</span>
                    </div>
                  </div>

                  <div class="grid md:grid-cols-2 gap-8">
                    <!-- Operating Hours List -->
                    <div class="bg-white dark:bg-muted-900 rounded-xl shadow-sm border border-muted-200 dark:border-muted-700 p-3">
                      <template v-if="spaceData.space.operation">
                        <div 
                          v-for="day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']" 
                          :key="day"
                          class="flex items-center justify-between p-3.5 border-b border-muted-200 dark:border-muted-700 last:border-0 transition-colors"
                          :class="[
                            getCurrentDayOfWeek() === day 
                              ? 'bg-primary-50/50 dark:bg-primary-500/5 rounded-lg' 
                              : 'hover:bg-muted-50 dark:hover:bg-muted-800/50'
                          ]"
                        >
                          <div class="flex items-center gap-3">
                            <div class="size-2.5 rounded-full"
                                 :class="spaceData.space.operation[day].isOpen ? 'bg-success-500' : 'bg-danger-500'"
                            ></div>
                            <span class="font-medium capitalize">{{ day }}</span>
                          </div>
                          <div class="flex items-center gap-3">
                            <span 
                              class="font-mono text-sm px-2.5 py-1 rounded"
                              :class="[
                                spaceData.space.operation[day].isOpen 
                                  ? 'bg-success-50 dark:bg-success-500/10 text-success-700 dark:text-success-400' 
                                  : 'bg-danger-50 dark:bg-danger-500/10 text-danger-600 dark:text-danger-400'
                              ]"
                            >
                              {{ formatOperationHours(spaceData.space.operation[day]) }}
                            </span>
                            <div 
                              v-if="getCurrentDayOfWeek() === day"
                              class="size-5 rounded-full bg-primary-500 flex items-center justify-center"
                            >
                              <Icon 
                                name="ph:arrow-right" 
                                class="size-3 text-white"
                              />
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>

                    <!-- Info Cards -->
                    <div class="space-y-4">
                      <!-- Today's Hours Card -->
                      <div class="p-4 rounded-xl bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-500/10 dark:to-primary-500/20 border border-primary-100 dark:border-primary-500/20">
                        <div class="flex items-start gap-3">
                          <div class="shrink-0">
                            <div class="size-10 rounded-lg bg-white dark:bg-primary-500/30 shadow-sm flex items-center justify-center">
                              <Icon name="ph:calendar-check" class="size-5 text-primary-500" />
                            </div>
                          </div>
                          <div>
                            <h4 class="font-medium mb-1">Today's Schedule</h4>
                            <p class="text-primary-600/80 dark:text-primary-400/80 font-medium">{{ todaysHours }}</p>
                            <p v-if="isCurrentlyOpen && spaceData.space.opensUntil" 
                               class="text-sm text-primary-600 dark:text-primary-400 font-medium mt-2 flex items-center gap-1.5"
                            >
                              <Icon name="ph:timer" class="size-4" />
                              Open until {{ spaceData.space.opensUntil }}
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Access Card -->
                      <div class="p-4 rounded-xl bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                        <div class="flex items-start gap-3">
                          <div class="shrink-0">
                            <div class="size-10 rounded-lg bg-white dark:bg-muted-900 shadow-sm flex items-center justify-center">
                              <Icon name="ph:key" class="size-5 text-muted-500" />
                            </div>
                          </div>
                          <div>
                            <h4 class="font-medium mb-1">Access Information</h4>
                            <p class="text-sm text-muted-500">Members can access the workspace during operating hours</p>
                          </div>
                        </div>
                      </div>

                      <!-- Support Card -->
                      <div class="p-4 rounded-xl bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                        <div class="flex items-start gap-3">
                          <div class="shrink-0">
                            <div class="size-10 rounded-lg bg-white dark:bg-muted-900 shadow-sm flex items-center justify-center">
                              <Icon name="ph:headset" class="size-5 text-muted-500" />
                            </div>
                          </div>
                          <div>
                            <h4 class="font-medium mb-1">On-site Support</h4>
                            <p class="text-sm text-muted-500">Staff available during business hours for assistance and inquiries.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </BaseCard>

              <!-- House Rules -->
              <BaseCard color="default" rounded="lg" class="mb-8">
                <div class="p-6 relative overflow-hidden">
                  <!-- Background Pattern -->
                  <div class="absolute -right-10 -top-10 opacity-5 dark:opacity-[0.03]">
                    <Icon name="ph:note-pencil" class="size-48 text-primary-500" />
                  </div>
                  
                  <div class="flex items-center gap-3 mb-6 relative">
                    <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/20 flex items-center justify-center">
                      <Icon name="ph:list-checks-duotone" class="size-5 text-primary-500" />
                    </div>
                    <h3 class="text-lg font-semibold">House Rules</h3>
                  </div>

                  <div class="grid md:grid-cols-2 gap-8">
                    <!-- Rules List -->
                    <div class="bg-white dark:bg-muted-900 rounded-xl shadow-sm border border-muted-200 dark:border-muted-700 p-5">
                      <div class="space-y-4">
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-amber-50 dark:bg-amber-500/10 flex items-center justify-center">
                            <Icon name="ph:chat-centered-text" class="size-4 text-amber-500" />
                          </div>
                          <div>
                            <h4 class="font-medium text-sm mb-1">Quiet Environment</h4>
                            <p class="text-sm text-muted-500">Please keep noise to a minimum. Use phone booths for calls.</p>
                          </div>
                        </div>
                        
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
                            <Icon name="ph:trash" class="size-4 text-primary-500" />
                          </div>
                          <div>
                            <h4 class="font-medium text-sm mb-1">Clean Your Space</h4>
                            <p class="text-sm text-muted-500">Clear your desk and dispose of trash before leaving.</p>
                          </div>
                        </div>
                        
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-amber-50 dark:bg-amber-500/10 flex items-center justify-center">
                            <Icon name="ph:coffee" class="size-4 text-amber-500" />
                          </div>
                          <div>
                            <h4 class="font-medium text-sm mb-1">Shared Kitchen Etiquette</h4>
                            <p class="text-sm text-muted-500">Clean dishes promptly. Label personal food items in refrigerator.</p>
                          </div>
                        </div>
                        
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-danger-50 dark:bg-danger-500/10 flex items-center justify-center">
                            <Icon name="ph:prohibit-duotone" class="size-4 text-danger-500" />
                          </div>
                          <div>
                            <h4 class="font-medium text-sm mb-1">No Smoking</h4>
                            <p class="text-sm text-muted-500">Smoking is not permitted anywhere inside the building.</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Additional Guidelines -->
                    <div class="space-y-4">
                      <!-- Security and Access -->
                      <div class="p-4 rounded-xl bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-500/10 dark:to-primary-500/20 border border-primary-100 dark:border-primary-500/20">
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
                            <Icon name="ph:key-duotone" class="size-4 text-primary-500" />
                          </div>
                          <div>
                            <h4 class="font-medium mb-1">Security and Access</h4>
                            <p class="text-sm text-primary-600/80 dark:text-primary-400/80">Members are responsible for their belongings. Use your access card at all times and don't share it with others.</p>
                          </div>
                        </div>
                      </div>

                      <!-- Meeting Room Usage -->
                      <div class="p-4 rounded-xl bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-muted-100 dark:bg-muted-700 flex items-center justify-center">
                            <Icon name="ph:chalkboard-teacher" class="size-4 text-muted-500" />
                          </div>
                          <div>
                            <h4 class="font-medium mb-1">Meeting Rooms</h4>
                            <p class="text-sm text-muted-500">Please book meeting rooms in advance. Release your booking if you no longer need the space.</p>
                          </div>
                        </div>
                      </div>

                      <!-- Code of Conduct -->
                      <div class="p-4 rounded-xl bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                        <div class="flex items-start gap-3">
                          <div class="size-8 shrink-0 rounded-full bg-muted-100 dark:bg-muted-700 flex items-center justify-center">
                            <Icon name="ph:handshake" class="size-4 text-muted-500" />
                          </div>
                          <div>
                            <h4 class="font-medium mb-1">Professional Conduct</h4>
                            <p class="text-sm text-muted-500">Please treat all members and staff with respect. Harassment of any kind will not be tolerated.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </BaseCard>

              <!-- Membership Options -->
              <BaseCard 
                v-if="hasMemberships"
                color="default" 
                rounded="lg" 
                class="mb-8"
              >
                <div class="p-6 relative overflow-hidden">
                  <!-- Background Pattern -->
                  <div class="absolute -right-10 -top-10 opacity-5 dark:opacity-[0.03]">
                    <Icon name="ph:ticket" class="size-48 text-primary-500" />
                  </div>
                  
                  <div class="flex items-center gap-3 mb-6 relative">
                    <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/20 flex items-center justify-center">
                      <Icon name="ph:medal-duotone" class="size-5 text-primary-500" />
                    </div>
                    <h3 class="text-lg font-semibold">Membership Plans</h3>
                  </div>




<div class="grid md:grid-cols-2 gap-6">
    <div
      v-if="!pendingMemberships"
      v-for="(membership, index) in activeMemberships" 
      :key="membership.id"
      class="bg-white dark:bg-muted-900 rounded-xl shadow-sm overflow-hidden border border-muted-200 dark:border-muted-700 hover:shadow-md transition-all duration-300 flex flex-col h-full"
    >
      <!-- Header Section -->
      <div 
        class="p-5 border-b"
        :class="getMembershipHeaderClasses(membership.type)"
      >
        <div class="flex items-center justify-between mb-3">
          <h4 
            class="font-semibold"
            :class="getMembershipTitleClasses(membership.type)"
          >
            {{ membership.type }} Membership
          </h4>
          <div 
            class="px-3 py-1 rounded-full text-xs font-medium"
            :class="getMembershipBadgeClasses(membership.type)"
          >
            {{ getMembershipBadgeText(membership.type, index) }}
          </div>
        </div>
        
        <div class="flex items-end gap-1 mb-2">
          <span class="text-3xl font-bold">{{ formatPrice(membership.price) }}</span>
          <span class="text-muted-500 pb-1">/{{ membership.type.toLowerCase() === 'yearly' ? 'year' : 'month' }}</span>
        </div>
        
        <p 
          class="text-sm mb-1"
          :class="getMembershipDescriptionClasses(membership.type)"
        >
          {{ getMembershipDescription(membership) }}
        </p>
      </div>
      
      <!-- Content Section -->
      <div class="p-5 flex-1 flex flex-col">
        <div class="space-y-3 flex-1">
          <div 
            v-for="feature in membership.features" 
            :key="feature"
            class="flex items-center gap-2"
          >
            <Icon name="ph:check-circle-fill" class="size-5 text-muted-500 dark:text-muted-400" />
            <span class="text-sm">{{ feature }}</span>
          </div>
        </div>
        
        <BaseButton 
          v-if="userSession.isLoggedIn"
          :color="getMembershipButtonColor(membership.type)" 
          size="lg" 
          class="w-full mt-5 rounded-full h-12"
          @click="selectMembership(membership)"
        >
          Choose {{ membership.type }} Plan
        </BaseButton>
        <BaseButton 
          v-else
          color="default" 
          size="lg" 
          class="w-full mt-5 rounded-full h-12"
          @click="loginModal.open()"
        >
          Login to Subscribe
        </BaseButton>
      </div>
    </div>
  </div>

                  
<!-- How It Works -->
<div class="mt-8 p-5 bg-muted-50 dark:bg-muted-800/50 rounded-xl border border-muted-200 dark:border-muted-700">
  <div class="flex items-start gap-4">
    <div>
      <h4 class="font-medium mb-2">How It Works</h4>
      <div class="grid sm:grid-cols-2 gap-3">
        <div class="flex items-start gap-2">
          <Icon name="healthicons:1" class="size-4 text-primary-500 mt-0.5 shrink-0" />
          <span class="text-sm text-muted-500">Submit your subscription request through our streamlined platform</span>
        </div>
        <div class="flex items-start gap-2">
          <Icon name="healthicons:2" class="size-4 text-primary-500 mt-0.5 shrink-0" />
          <span class="text-sm text-muted-500">Subscription owner receives instant notification for verification</span>
        </div>
        <div class="flex items-start gap-2">
          <Icon name="healthicons:3" class="size-4 text-primary-500 mt-0.5 shrink-0" />
          <span class="text-sm text-muted-500">Credits automatically added to your account upon confirmation</span>
        </div>
        <div class="flex items-start gap-2">
          <Icon name="healthicons:4" class="size-4 text-primary-500 mt-0.5 shrink-0" />
          <span class="text-sm text-muted-500">Smart auto-invoicing system handles all billing seamlessly</span>
        </div>
      </div>
    </div>
  </div>
</div>
</div>




                
              </BaseCard>

              <div v-else>
                <BaseCard color="default" rounded="lg" class="mb-8">
                  <div class="p-6">
                    <h4 class="text-lg font-semibold mb-2">No memberships available for this space.</h4>
                    <p class="text-muted-500">Please check back later or contact the space for more information.</p>
                  </div>
                </BaseCard>
              </div>

            </div>






            <!-- Amenities Tab -->
            <div v-if="activeTab === 'amenities'" class="space-y-8">
              <BaseCard rounded="lg" class="relative overflow-hidden">
                <div class="absolute -right-16 -top-16 opacity-5 dark:opacity-[0.03] pointer-events-none">
                  <Icon name="ph:squares-four" class="size-72 text-primary-500" />
                </div>
                
                <div class="p-6 relative">
                  <!-- Header -->
                  <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-6 mb-6 border-b border-muted-200 dark:border-muted-800">
                    <div class="flex items-center gap-4">
                      <div class="size-12 rounded-xl bg-primary-500/10 dark:bg-primary-500/20 flex items-center justify-center">
                        <Icon name="ph:puzzle-piece-duotone" class="size-6 text-primary-500" />
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold">Workspace Amenities</h3>
                        <p class="text-sm text-muted-500 mt-1">Discover everything this space has to offer</p>
                      </div>
                    </div>
                    
                    <div>
                      <BaseButton 
                        color="primary" 
                        class="rounded-full px-4"
                        @click="showAllAmenities = !showAllAmenities"
                      >
                        <div class="flex items-center gap-2">
                          <Icon :name="showAllAmenities ? 'ph:list-bullets' : 'ph:list-plus'" class="size-4" />
                          <span>{{ showAllAmenities ? 'Collapse All' : 'Expand All' }}</span>
                        </div>
                      </BaseButton>
                    </div>
                  </div>
                  
                  <div v-if="spaceData.space.amenities && spaceData.space.amenities.length > 0">
                    <!-- Amenities by category -->
                    <div v-for="(category, index) in amenityCategories" :key="category.name" 
                         class="mb-2 last:mb-0 bg-white dark:bg-muted-900 rounded-xl border border-muted-200 dark:border-muted-700 shadow-sm overflow-hidden transition-all duration-300"
                         :class="expandedCategories.includes(index) ? 'ring-1 ring-primary-300 dark:ring-primary-700' : ''">
                      <div 
                        class="flex items-center justify-between p-2 cursor-pointer transition-colors"
                        :class="expandedCategories.includes(index) ? 'bg-primary-50/70 dark:bg-primary-500/10' : 'hover:bg-muted-50 dark:hover:bg-muted-800/50'"
                        @click="toggleCategory(index)"
                      >
                        <div class="flex items-center gap-3">
                          <div class="size-10 rounded-xl bg-gradient-to-br from-primary-500/10 to-primary-500/5 dark:from-primary-500/20 dark:to-primary-500/10 flex items-center justify-center border border-primary-100 dark:border-primary-500/20">
                            <Icon :name="category.icon" class="size-5 text-primary-500" />
                          </div>
                          <h4 class="font-medium">{{ category.name }}</h4>
                          <div class="ml-2 px-2 py-0.5 text-xs rounded-full bg-primary-50 dark:bg-primary-500/10 text-primary-600 dark:text-primary-400">
                            {{ category.items.length }}
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <span class="text-sm text-muted-500">{{ expandedCategories.includes(index) ? 'Hide' : 'Show' }}</span>
                          <div class="size-8 rounded-full flex items-center justify-center bg-white dark:bg-muted-800 shadow-sm border border-muted-200 dark:border-muted-700 transition-transform duration-300"
                               :class="expandedCategories.includes(index) ? 'rotate-180' : ''">
                            <Icon 
                              name="ph:caret-down" 
                              class="size-4 text-primary-500"
                            />
                          </div>
                        </div>
                      </div>
                      
                      <div v-if="expandedCategories.includes(index) || showAllAmenities" 
                           class="divide-y divide-muted-200 dark:divide-muted-700 p-2">
                        <div 
                          v-for="amenity in category.items" 
                          :key="amenity.id"
                          class="flex items-start gap-3 p-3 rounded-lg hover:bg-muted-50 dark:hover:bg-muted-800/50 transition-colors duration-200 group"
                        >
                          <div class="mt-0.5 size-6 rounded-full bg-muted-100 dark:bg-muted-800 flex items-center justify-center shrink-0 shadow-sm border border-muted-200 dark:border-muted-700 group-hover:scale-110 transition-transform">
                            <Icon name="ph:check" class="size-3.5 text-muted-500 dark:text-muted-400" />
                          </div>
                          <div>
                            <div class="font-medium text-sm group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">{{ amenity.name }}</div>
                            <div class="text-xs text-muted-500 mt-0.5">{{ amenity.description }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </BaseCard>
              
              <!-- Featured Amenities -->
              <div class="flex items-center justify-between mb-5">
                <h4 class="text-base font-medium text-muted-700 dark:text-muted-300">Featured Amenities</h4>
                <div class="h-px flex-1 bg-muted-200 dark:bg-muted-700 ml-4"></div>
              </div>
              
              <!-- Additional amenities cards -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="p-5 bg-white dark:bg-muted-900 rounded-lg border border-muted-200 dark:border-muted-800 shadow-sm hover:shadow-md transition-all duration-300">
                  <div class="flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-lg bg-amber-50 dark:bg-amber-500/10 flex items-center justify-center mb-3">
                      <Icon name="ph:coffee" class="size-6 text-amber-500" />
                    </div>
                    <h4 class="font-medium text-muted-800 dark:text-muted-200 mb-2">Refreshments</h4>
                    <p class="text-sm text-muted-500 dark:text-muted-400 mb-3">Complimentary beverages</p>
                    <div class="text-xs text-muted-500 dark:text-muted-400 flex items-center gap-1.5">
                      <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400" />
                      <span>Daily service</span>
                    </div>
                  </div>
                </div>
                
                <div class="p-5 bg-white dark:bg-muted-900 rounded-lg border border-muted-200 dark:border-muted-800 shadow-sm hover:shadow-md transition-all duration-300">
                  <div class="flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-lg bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center mb-3">
                      <Icon name="ph:wifi-high" class="size-6 text-primary-500" />
                    </div>
                    <h4 class="font-medium text-muted-800 dark:text-muted-200 mb-2">Fast Internet</h4>
                    <p class="text-sm text-muted-500 dark:text-muted-400 mb-3">Secure fiber connection</p>
                    <div class="text-xs text-muted-500 dark:text-muted-400 flex items-center gap-1.5">
                      <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400" />
                      <span>1 Gbps speed</span>
                    </div>
                  </div>
                </div>
                
                <div class="p-5 bg-white dark:bg-muted-900 rounded-lg border border-muted-200 dark:border-muted-800 shadow-sm hover:shadow-md transition-all duration-300">
                  <div class="flex flex-col items-center text-center">
                    <div class="w-12 h-12 rounded-lg bg-muted-100 dark:bg-muted-800 flex items-center justify-center mb-3">
                      <Icon name="ph:desktop" class="size-6 text-muted-600 dark:text-muted-400" />
                    </div>
                    <h4 class="font-medium text-muted-800 dark:text-muted-200 mb-2">Workstations</h4>
                    <p class="text-sm text-muted-500 dark:text-muted-400 mb-3">Ergonomic design</p>
                    <div class="text-xs text-muted-500 dark:text-muted-400 flex items-center gap-1.5">
                      <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400" />
                      <span>Adjustable height</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Rooms Tab -->
            <div v-if="activeTab === 'rooms'" class="space-y-8">
              <div class="bg-white dark:bg-muted-900/95 rounded-2xl p-6 border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md">
                <!-- Header with booking type toggle -->
                <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
                  <div>
                    <h3 class="text-lg font-semibold">Available Spaces</h3>
                    <p class="text-sm text-muted-500 mt-1">Find the perfect space for your needs</p>
                  </div>
                  <div class="flex items-center gap-3 bg-muted-50 dark:bg-muted-800 p-1.5 rounded-full">
                    <button 
                      v-for="type in ['hourly', 'daily']"
                      :key="type"
                      class="px-5 py-2 text-sm font-medium rounded-full transition-all duration-200"
                      :class="bookingType === type ? 
                        'bg-white dark:bg-muted-900 text-primary-600 dark:text-primary-400 shadow-sm' : 
                        'text-muted-600 dark:text-muted-400 hover:text-primary-600 dark:hover:text-primary-400'"
                      @click="bookingType = type"
                    >
                      <span class="flex items-center gap-2">
                        <Icon :name="type === 'hourly' ? 'ph:clock' : 'ph:calendar'" class="size-4" />
                        {{ type.charAt(0).toUpperCase() + type.slice(1) }}
                      </span>
                    </button>
                  </div>
                </div>

                <div v-if="spaceData.space.rooms && spaceData.space.rooms.length > 0">
                  <!-- Room Cards -->
                  <div class="grid grid-cols-1 gap-6">
                    <div 
                      v-for="room in spaceData.space.rooms" 
                      :key="room.id"
                      class="group relative overflow-hidden rounded-xl border transition-all duration-300"
                      :class="selectedRoom && selectedRoom.id === room.id ? 
                        'border-primary-400 dark:border-primary-400 bg-white dark:bg-muted-900 ring-1 ring-primary-400' : 
                        'border-muted-200 dark:border-muted-700 bg-white dark:bg-muted-900 hover:bg-muted-50 dark:hover:bg-muted-800/50'"
                    >
                      <div class="p-6">
                        <!-- Room Details -->
                        <div class="flex flex-col">
                          <div class="flex items-start justify-between gap-4 mb-4">
                            <div>
                              <div class="flex items-center gap-2 mb-1">
                                <span class="px-3 py-1 text-xs font-medium bg-primary-50 dark:bg-primary-500/10 rounded-full text-primary-600 dark:text-primary-400">
                                  {{ room.type.type }}
                                </span>
                                <span class="px-3 py-1 text-xs font-medium bg-success-50 dark:bg-success-500/10 rounded-full text-success-600 dark:text-success-400 flex items-center gap-1">
                                  <Icon name="ph:check-circle" class="size-3" />
                                  Available
                                </span>
                              </div>
                              <h4 class="text-xl font-semibold">{{ room.name }}</h4>
                              <p class="text-muted-500 mt-2">
                                A professional space perfect for {{ room.type.type === 'Office Space' ? 'team collaboration' : 'focused individual work' }}. 
                                Designed for maximum productivity.
                              </p>
                            </div>
                            <div class="text-right shrink-0">
                              <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                {{ formatPrice(bookingType === 'hourly' ? room.pricePerHour : room.pricePerDay) }}
                              </div>
                              <div class="text-sm text-muted-500">per {{ bookingType === 'hourly' ? 'hour' : 'day' }}</div>
                            </div>
                          </div>

                          <!-- Actions -->
                          <div class="flex items-center justify-end gap-3 mt-4 pt-4 border-t border-muted-200 dark:border-muted-700">
                            <button 
                              v-if="!(selectedRoom && selectedRoom.id === room.id)"
                              class="px-4 py-2 rounded-lg text-sm font-medium border border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors"
                              @click="selectedRoom = room"
                            >
                              View Details
                            </button>
                            <button 
                              class="px-4 py-2 rounded-lg text-sm font-medium bg-primary-500 hover:bg-primary-600 text-white transition-colors"
                              @click="selectedRoom = room; $el.closest('.page-content').querySelector('#booking-section').scrollIntoView({ behavior: 'smooth' })"
                            >
                              {{ selectedRoom && selectedRoom.id === room.id ? 'Book Now' : 'Select & Book' }}
                            </button>
                          </div>
                        </div>
                      </div>

                      <!-- Expanded Details (when selected) -->
                      <div 
                        v-if="selectedRoom && selectedRoom.id === room.id"
                        class="p-6 bg-muted-50 dark:bg-muted-800/50 border-t border-muted-200 dark:border-muted-700"
                      >
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <h5 class="font-medium text-sm mb-2">Room Specifications</h5>
                            <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400">
                              <li class="flex items-center gap-2">
                                <Icon name="ph:users" class="size-4 text-primary-500" />
                                <span>Up to {{ room.capacity }} people</span>
                              </li>
                              <li class="flex items-center gap-2">
                                <Icon name="ph:door" class="size-4 text-primary-500" />
                                <span>{{ room.type.type === 'Office Space' ? 'Private entrance' : 'Shared entrance' }}</span>
                              </li>
                              <li class="flex items-center gap-2">
                                <Icon name="ph:sun" class="size-4 text-primary-500" />
                                <span>Natural lighting</span>
                              </li>
                            </ul>
                          </div>
                          
                          <div>
                            <h5 class="font-medium text-sm mb-2">Included Amenities</h5>
                            <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400">
                              <li class="flex items-center gap-2">
                                <Icon name="ph:wifi-high" class="size-4 text-primary-500" />
                                <span>High-speed WiFi</span>
                              </li>
                              <li class="flex items-center gap-2">
                                <Icon name="ph:coffee" class="size-4 text-primary-500" />
                                <span>Complimentary beverages</span>
                              </li>
                              <li class="flex items-center gap-2">
                                <Icon name="ph:chalkboard" class="size-4 text-primary-500" />
                                <span>Whiteboard</span>
                              </li>
                            </ul>
                          </div>
                          
                          <div>
                            <h5 class="font-medium text-sm mb-2">Booking Options</h5>
                            <div class="space-y-3">
                              <div class="flex items-center justify-between text-sm">
                                <span>Hourly rate:</span>
                                <span class="font-medium">{{ formatPrice(room.pricePerHour) }}</span>
                              </div>
                              <div class="flex items-center justify-between text-sm">
                                <span>Daily rate:</span>
                                <span class="font-medium">{{ formatPrice(room.pricePerDay) }}</span>
                              </div>
                              <div class="flex items-center justify-between text-sm">
                                <span>Capacity:</span>
                                <span class="font-medium">{{ room.capacity }} people</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-muted-200 dark:border-muted-700">
                          <div class="text-sm text-muted-500">
                            <Icon name="ph:info-duotone" class="size-4 inline-block mr-1" />
                            Room availability is subject to change. Book now to secure your space.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Room Comparison -->
              <div v-if="spaceData.space.rooms && spaceData.space.rooms.length > 1" class="bg-white dark:bg-muted-900/95 rounded-2xl p-6 border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md">
                <h3 class="text-lg font-semibold mb-6">Room Comparison</h3>
                
                <div class="overflow-x-auto">
                  <table class="w-full min-w-[600px]">
                    <thead>
                      <tr class="border-b border-muted-200 dark:border-muted-700">
                        <th class="py-3 px-4 text-left text-sm font-medium text-muted-700 dark:text-muted-300">Room Type</th>
                        <th class="py-3 px-4 text-left text-sm font-medium text-muted-700 dark:text-muted-300">Capacity</th>
                        <th class="py-3 px-4 text-left text-sm font-medium text-muted-700 dark:text-muted-300">Hourly Rate</th>
                        <th class="py-3 px-4 text-left text-sm font-medium text-muted-700 dark:text-muted-300">Daily Rate</th>
                        <th class="py-3 px-4 text-left text-sm font-medium text-muted-700 dark:text-muted-300">Features</th>
                        <th class="py-3 px-4 text-center text-sm font-medium text-muted-700 dark:text-muted-300">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr 
                        v-for="room in spaceData.space.rooms" 
                        :key="room.id"
                        class="border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50 transition-colors"
                      >
                        <td class="py-4 px-4">
                          <div class="font-medium">{{ room.name }}</div>
                          <div class="text-xs text-muted-500">{{ room.type.type }}</div>
                        </td>
                        <td class="py-4 px-4">
                          <div class="flex items-center gap-1.5">
                            <Icon name="ph:users" class="size-4 text-muted-500" />
                            <span>{{ room.capacity }}</span>
                          </div>
                        </td>
                        <td class="py-4 px-4 font-medium">{{ formatPrice(room.pricePerHour) }}</td>
                        <td class="py-4 px-4 font-medium">{{ formatPrice(room.pricePerDay) }}</td>
                        <td class="py-4 px-4">
                          <div class="flex flex-wrap gap-1">
                            <span class="inline-block px-2 py-0.5 bg-muted-100 dark:bg-muted-800 rounded-full text-xs">WiFi</span>
                            <span class="inline-block px-2 py-0.5 bg-muted-100 dark:bg-muted-800 rounded-full text-xs">Power</span>
                            <span class="inline-block px-2 py-0.5 bg-muted-100 dark:bg-muted-800 rounded-full text-xs">HVAC</span>
                          </div>
                        </td>
                        <td class="py-4 px-4 text-center">
                          <button 
                            class="px-3 py-1.5 rounded-lg text-xs font-medium bg-primary-500 hover:bg-primary-600 text-white transition-colors"
                            @click="selectedRoom = room; $el.closest('.page-content').querySelector('#booking-section').scrollIntoView({ behavior: 'smooth' })"
                          >
                            Select
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              
              <!-- Room Booking Tips -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white dark:bg-muted-900/95 rounded-2xl p-5 border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md">
                  <div class="flex items-start gap-3">
                    <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
                      <Icon name="ph:calendar-check" class="size-5 text-primary-500" />
                    </div>
                    <div>
                      <h4 class="font-medium text-sm">Flexible Booking</h4>
                      <p class="text-xs text-muted-500 mt-1">Book by the hour or day to match your exact needs. No long-term commitments required.</p>
                    </div>
                  </div>
                </div>
                
                <div class="bg-white dark:bg-muted-900/95 rounded-2xl p-5 border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md">
                  <div class="flex items-start gap-3">
                    <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
                      <Icon name="ph:users-three" class="size-5 text-primary-500" />
                    </div>
                    <div>
                      <h4 class="font-medium text-sm">Team Bookings</h4>
                      <p class="text-xs text-muted-500 mt-1">Special rates available for team bookings. Contact the workspace for custom arrangements.</p>
                    </div>
                  </div>
                </div>
                
                <div class="bg-white dark:bg-muted-900/95 rounded-2xl p-5 border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md">
                  <div class="flex items-start gap-3">
                    <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
                      <Icon name="ph:medal" class="size-5 text-primary-500" />
                    </div>
                    <div>
                      <h4 class="font-medium text-sm">Premium Experience</h4>
                      <p class="text-xs text-muted-500 mt-1">All rooms include premium amenities and professional environment to enhance productivity.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Events Tab -->
            <div v-if="activeTab === 'events'" class="space-y-8">
              <BaseCard rounded="lg" class="relative overflow-hidden">
                <div class="absolute -right-16 -top-16 opacity-5 dark:opacity-[0.03] pointer-events-none">
                  <Icon name="ph:calendar-dots" class="size-72 text-primary-500" />
                </div>

                <div class="p-6 relative">
                  <!-- Header -->
                  <div class="pb-6 mb-6 border-b border-muted-200 dark:border-muted-800">
                    <div class="flex items-center gap-4">
                      <div class="size-12 rounded-xl bg-primary-500/10 dark:bg-primary-500/20 flex items-center justify-center">
                        <Icon name="ph:calendar-check-duotone" class="size-6 text-primary-500" />
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold">Upcoming Events</h3>
                        <p class="text-sm text-muted-500 mt-1">Discover exciting events happening at this space</p>
                      </div>
                    </div>
                  </div>

                  <!-- Events Loading State -->
                  <div v-if="pendingEvents" class="space-y-4">
                    <div v-for="i in 3" :key="i" class="animate-pulse">
                      <div class="bg-white dark:bg-muted-900 rounded-xl border border-muted-200 dark:border-muted-700 p-6">
                        <div class="flex items-start gap-4">
                          <div class="size-12 bg-muted-200 dark:bg-muted-700 rounded-xl"></div>
                          <div class="flex-1 space-y-3">
                            <div class="h-4 bg-muted-200 dark:bg-muted-700 rounded w-20"></div>
                            <div class="h-5 bg-muted-200 dark:bg-muted-700 rounded w-3/4"></div>
                            <div class="h-4 bg-muted-200 dark:bg-muted-700 rounded w-1/2"></div>
                            <div class="flex gap-4">
                              <div class="h-4 bg-muted-200 dark:bg-muted-700 rounded w-24"></div>
                              <div class="h-4 bg-muted-200 dark:bg-muted-700 rounded w-20"></div>
                            </div>
                          </div>
                          <div class="h-8 bg-muted-200 dark:bg-muted-700 rounded w-20"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Events Error State -->
                  <div v-else-if="eventsError" class="text-center py-12">
                    <div class="size-16 rounded-xl bg-warning-500/10 dark:bg-warning-500/20 flex items-center justify-center mx-auto mb-4">
                      <Icon name="ph:warning-circle-duotone" class="size-8 text-warning-500" />
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Unable to load events</h3>
                    <p class="text-muted-600 dark:text-muted-400 mb-4">
                      There was an error loading events for this space.
                    </p>
                    <BaseButton @click="fetchSpaceEvents" size="sm" color="primary" class="rounded-full">
                      <Icon name="ph:arrow-clockwise-duotone" class="size-4" />
                      <span>Try Again</span>
                    </BaseButton>
                  </div>

                  <!-- No Events State -->
                  <div v-else-if="!events.length" class="text-center py-12">
                    <div class="size-16 rounded-xl bg-muted-500/10 dark:bg-muted-500/20 flex items-center justify-center mx-auto mb-4">
                      <Icon name="ph:calendar-x-duotone" class="size-8 text-muted-500" />
                    </div>
                    <h3 class="text-lg font-semibold mb-2">No upcoming events</h3>
                    <p class="text-muted-600 dark:text-muted-400">
                      There are currently no upcoming events scheduled at this space.
                    </p>
                  </div>

                  <!-- Events List -->
                  <div v-else class="space-y-3">
                    <div
                      v-for="(event, index) in events"
                      :key="event.id"
                      class="group bg-white dark:bg-muted-900 rounded-lg border border-muted-200 dark:border-muted-700 transition-all duration-200 hover:border-muted-300 dark:hover:border-muted-600 hover:shadow-sm"
                    >
                      <div class="flex items-center p-4 gap-4">
                        <!-- Event Image or Date -->
                        <div class="flex-shrink-0">
                          <div v-if="event.imageUrl" class="relative w-12 h-12 rounded-lg overflow-hidden">
                            <img
                              :src="event.imageUrl"
                              :alt="event.title"
                              class="w-full h-full object-cover"
                            />
                          </div>
                          <div v-else class="w-12 h-12 bg-muted-100 dark:bg-muted-800 rounded-lg flex flex-col items-center justify-center">
                            <div class="text-xs font-medium text-muted-600 dark:text-muted-400 leading-none">
                              {{ formatEventDate(event.startDateTime).split(' ')[0] }}
                            </div>
                            <div class="text-sm font-semibold text-muted-800 dark:text-muted-200 leading-none">
                              {{ formatEventDate(event.startDateTime).split(' ')[1] }}
                            </div>
                          </div>
                        </div>

                        <!-- Event Content -->
                        <div class="flex-1 min-w-0">
                          <!-- Event Title & Type -->
                          <div class="flex items-center gap-2 mb-1">
                            <h4 class="font-medium text-muted-900 dark:text-muted-100 truncate">
                              {{ event.title }}
                            </h4>
                            <span class="px-2 py-0.5 text-xs font-medium rounded bg-muted-100 dark:bg-muted-800 text-muted-600 dark:text-muted-400 flex-shrink-0">
                              {{ event.type.charAt(0).toUpperCase() + event.type.slice(1) }}
                            </span>
                            <span v-if="event.isOnline" class="px-2 py-0.5 text-xs font-medium bg-green-100 dark:bg-green-900/30 rounded text-green-700 dark:text-green-400 flex-shrink-0">
                              Online
                            </span>
                          </div>

                          <!-- Event Meta Info -->
                          <div class="flex items-center gap-4 text-sm text-muted-500 dark:text-muted-400">
                            <span>{{ formatEventTime(event.startDateTime, event.endDateTime) }}</span>
                            <span>•</span>
                            <span>{{ getEventDuration(event.startDateTime, event.endDateTime) }}</span>
                            <span v-if="event.location || event.room?.name">•</span>
                            <span v-if="event.location || event.room?.name" class="truncate">{{ event.room?.name || event.location }}</span>
                            <span>•</span>
                            <span>{{ event.attendeesCount || 0 }}<span v-if="event.maxAttendees">/{{ event.maxAttendees }}</span> attending</span>
                          </div>
                        </div>

                        <!-- Price & Action -->
                        <div class="flex items-center gap-3 flex-shrink-0">
                          <!-- Price -->
                          <div v-if="event.price && event.price > 0" class="text-sm font-medium text-muted-900 dark:text-muted-100">
                            {{ formatPrice(event.price) }}
                          </div>
                          <div v-else class="text-sm font-medium text-green-600 dark:text-green-400">
                            Free
                          </div>

                          <!-- Action Button -->
                          <BaseButton
                            color="primary"
                            size="xs"
                            class="rounded-md px-3"
                          >
                            <span>View</span>
                          </BaseButton>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </BaseCard>
            </div>

            <!-- Location Tab -->
            <div v-if="activeTab === 'location'" class="space-y-8">
              <BaseCard rounded="lg" class="relative overflow-hidden">
                <div class="absolute -right-16 -top-16 opacity-5 dark:opacity-[0.03] pointer-events-none">
                  <Icon name="ph:map-pin" class="size-72 text-primary-500" />
                </div>

                <div class="p-6 relative">
                  <!-- Header -->
                  <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-6 mb-6 border-b border-muted-200 dark:border-muted-800">
                    <div class="flex items-center gap-4">
                      <div class="size-12 rounded-xl bg-primary-500/10 dark:bg-primary-500/20 flex items-center justify-center">
                        <Icon name="ph:map-pin-duotone" class="size-6 text-primary-500" />
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold">Location & Transport</h3>
                        <p class="text-sm text-muted-500 mt-1">{{ spaceData.space.address }}</p>
                      </div>
                    </div>
                  </div>

                <!-- Interactive Map -->
                <div class="aspect-[16/9] rounded-lg overflow-hidden mb-6 border border-muted-200 dark:border-muted-700">
                  <LocationMap
                    v-if="spaceData?.space?.location?.coordinates"
                    ref="locationMapRef"
                    :center="[spaceData.space.location.coordinates[0], spaceData.space.location.coordinates[1]]"
                    :zoom="13"
                    :location="{
                      name: spaceData.space.name,
                      address: spaceData.space.address,
                      coordinates: [spaceData.space.location.coordinates[0], spaceData.space.location.coordinates[1]],
                      rating: parseFloat(spaceData.space.rating) || 0,
                      image: spaceData.space.mainImage || '',
                      price: spaceData.space.rooms?.[0]?.pricePerDay || 0
                    }"
                    :show-transport="true"
                    class="w-full h-full"
                  />
                  <div v-else class="w-full h-full bg-muted-50 dark:bg-muted-800 flex items-center justify-center">
                    <div class="text-center">
                      <Icon name="ph:map-pin" class="size-8 text-muted-400 mb-2" />
                      <p class="text-sm text-muted-500">Location not available</p>
                    </div>
                  </div>
                </div>
                
                <!-- Location Details Grid -->
                <div class="grid md:grid-cols-3 gap-4">
                  <!-- Google Maps Link -->
                  <div class="p-4 rounded bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                    <div class="flex items-center gap-2 mb-2">
                      <Icon name="ph:map-pin" class="size-3.5 text-muted-600 dark:text-muted-400" />
                      <h4 class="font-medium text-xs text-muted-900 dark:text-white">External Map</h4>
                    </div>
                    <BaseLink
                      :href="`https://www.google.com/maps?q=${spaceData.space.location.coordinates[1]},${spaceData.space.location.coordinates[0]}`"
                      target="_blank"
                      rel="noopener noreferrer"
                      external
                      class="text-xs text-muted-600 dark:text-muted-400 hover:text-muted-700 dark:hover:text-muted-300 transition-colors duration-200 flex items-center gap-1 font-medium"
                    >
                      <span>Open in Google Maps</span>
                      <Icon name="ph:arrow-square-out" class="size-3" />
                    </BaseLink>
                  </div>

                  <!-- Parking Info -->
                  <div class="p-4 rounded bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                    <div class="flex items-center gap-2 mb-2">
                      <Icon name="ph:car" class="size-3.5 text-muted-600 dark:text-muted-400" />
                      <h4 class="font-medium text-xs text-muted-900 dark:text-white">Parking</h4>
                    </div>
                    <p class="text-xs text-muted-600 dark:text-muted-400">
                      {{ spaceData.space.amenities?.some(a => a.name.toLowerCase().includes('parking')) ? 'Available on-site' : 'Street parking' }}
                    </p>
                  </div>

                  <!-- Accessibility -->
                  <div class="p-4 rounded bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                    <div class="flex items-center gap-2 mb-2">
                      <Icon name="ph:wheelchair" class="size-3.5 text-muted-600 dark:text-muted-400" />
                      <h4 class="font-medium text-xs text-muted-900 dark:text-white">Accessibility</h4>
                    </div>
                    <p class="text-xs text-muted-600 dark:text-muted-400">
                      {{ spaceData.space.amenities?.some(a => a.name.toLowerCase().includes('wheelchair') || a.name.toLowerCase().includes('accessible')) ? 'Wheelchair accessible' : 'Contact for details' }}
                    </p>
                  </div>
                </div>

                <!-- Transport Information -->
                <div class="mt-6 p-5 rounded bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
                  <div class="flex items-center gap-2 mb-4">
                    <Icon name="ph:train" class="size-3.5 text-muted-600 dark:text-muted-400" />
                    <h4 class="font-medium text-sm text-muted-900 dark:text-white">Public Transport</h4>
                    <div v-if="locationMapRef?.isLoadingTransport" class="ml-auto">
                      <div class="animate-spin rounded-full h-3 w-3 border border-muted-300 border-t-muted-600"></div>
                    </div>
                  </div>

                  <!-- Nearest Transport Stops -->
                  <div v-if="locationMapRef?.nearestTransportStops?.length > 0" class="space-y-3 mb-4">
                    <div
                      v-for="stop in locationMapRef.nearestTransportStops"
                      :key="stop.id"
                      class="flex items-center gap-3 p-3 rounded bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700"
                    >
                      <div class="w-4 h-4 rounded-sm flex items-center justify-center" :class="getTransportBgColor(stop.type)">
                        <Icon :name="getTransportIcon(stop.type)" class="size-2" :class="getTransportTextColor(stop.type)" />
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-xs font-medium text-muted-900 dark:text-white truncate">{{ stop.name }}</p>
                        <p class="text-xs text-muted-600 dark:text-muted-400">{{ stop.walkingTimeText }} • {{ stop.distanceText }}</p>
                      </div>
                      <div class="text-xs text-muted-500 dark:text-muted-400 capitalize">{{ stop.type }}</div>
                    </div>
                  </div>

                  <!-- No transport data fallback -->
                  <div v-else-if="!locationMapRef?.isLoadingTransport && locationMapRef?.nearestTransportStops?.length === 0" class="mb-4 p-3 rounded bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700">
                    <div class="flex items-center gap-2 mb-2">
                      <Icon name="ph:info" class="size-3 text-muted-600 dark:text-muted-400" />
                      <span class="text-xs font-medium text-muted-900 dark:text-white">No nearby transport</span>
                    </div>
                    <p class="text-xs text-muted-600 dark:text-muted-400">
                      Best accessed by car or taxi.
                    </p>
                  </div>

                  <p class="text-xs text-muted-600 dark:text-muted-400 mb-4">
                    {{ locationMapRef?.nearestTransportStops?.length > 0
                        ? 'Click transport markers on the map for detailed information.'
                        : 'Transport information is being loaded...' }}
                  </p>

                  <div class="flex flex-wrap gap-2">
                    <div class="flex items-center gap-1 px-2 py-1 bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded text-xs">
                      <div class="w-2 h-2 bg-blue-600 dark:bg-blue-500 rounded-sm"></div>
                      <span class="text-muted-700 dark:text-muted-300">Train</span>
                    </div>
                    <div class="flex items-center gap-1 px-2 py-1 bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded text-xs">
                      <div class="w-2 h-2 bg-emerald-600 dark:bg-emerald-500 rounded-sm"></div>
                      <span class="text-muted-700 dark:text-muted-300">Tram</span>
                    </div>
                    <div class="flex items-center gap-1 px-2 py-1 bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded text-xs">
                      <div class="w-2 h-2 bg-amber-600 dark:bg-amber-500 rounded-sm"></div>
                      <span class="text-muted-700 dark:text-muted-300">Bus</span>
                    </div>
                  </div>
                </div>
                </div>
              </BaseCard>
            </div>

            <!-- Reviews Tab -->
            <div v-if="activeTab === 'reviews'" class="space-y-8">
              <!-- Reviews Summary -->
              <div
              class="bg-white dark:bg-muted-900/95 rounded-2xl p-6 border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md">
                <h3 class="text-lg font-semibold mb-6">Reviews</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <!-- Overall Rating -->
                  <div class="md:col-span-1">
                    <div class="flex flex-col items-center justify-center h-full">
                      <div class="flex items-center gap-2 mb-2">
                        <div class="text-4xl font-bold">{{ reviewStats.averageRating }}</div>
                        <div class="flex flex-col">
                          <div class="flex">
                            <Icon
                              v-for="i in 5"
                              :key="i"
                              :name="i <= Math.round(parseFloat(reviewStats.averageRating)) ? 'ph:star-fill' : 'ph:star'"
                              class="size-4 text-amber-500"
                            />
                          </div>
                          <div class="text-sm text-muted-500 mt-1">{{ reviewStats.totalReviews }} reviews</div>
                        </div>
                      </div>

                      <!-- Rating Distribution -->
                      <div class="w-full space-y-2 mt-4">
                        <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="flex items-center gap-2">
                          <div class="text-sm font-medium w-2">{{ rating }}</div>
                          <div class="flex-1 h-2 bg-muted-200 dark:bg-muted-700 rounded-full overflow-hidden">
                            <div
                              class="h-full bg-primary-500"
                              :style="`width: ${(reviewStats.ratingDistribution[rating] / reviewStats.totalReviews) * 100}%`"
                            ></div>
                          </div>
                          <div class="text-xs text-muted-500 w-8 text-right">{{ reviewStats.ratingDistribution[rating] }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Category Ratings -->
                  <div class="md:col-span-1">
                    <h4 class="font-medium text-sm mb-4">Rating Details</h4>
                    <div class="space-y-4">
                      <div v-for="(value, category) in reviewStats.categories" :key="category" class="space-y-1">
                        <div class="flex items-center justify-between">
                          <span class="text-sm capitalize">{{ category }}</span>
                          <span v-if="value" class="text-sm font-medium">{{ value }}</span>
                          <span v-else class="text-sm font-medium">0</span>
                        </div>
                        <div class="h-1.5 w-full bg-muted-200 dark:bg-muted-700 rounded-full overflow-hidden">
                          <div
                            class="h-full bg-primary-500"
                            :style="`width: ${(parseFloat(value) / 5) * 100}%`"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Review Filters -->
                  <div class="md:col-span-1">
                    <h4 class="font-medium text-sm mb-4">Filter Reviews</h4>

                    <div class="space-y-4">
                      <div>
                        <label class="text-xs text-muted-500 mb-1 block">Rating</label>
                        <select
                          v-model="reviewFilters.rating"
                          class="w-full px-3 py-2 bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg text-sm"
                        >
                          <option value="all">All ratings</option>
                          <option value="5">5 stars</option>
                          <option value="4">4 stars</option>
                          <option value="3">3 stars</option>
                          <option value="2">2 stars</option>
                          <option value="1">1 star</option>
                        </select>
                      </div>

                      <div>
                        <label class="text-xs text-muted-500 mb-1 block">Sort by</label>
                        <select
                          v-model="reviewFilters.sortBy"
                          class="w-full px-3 py-2 bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg text-sm"
                        >
                          <option value="recent">Most recent</option>
                          <option value="highest">Highest rated</option>
                          <option value="lowest">Lowest rated</option>
                          <option value="relevant">Most helpful</option>
                        </select>
                      </div>

                      <div class="flex items-center">
                        <BaseCheckbox
                          v-model="reviewFilters.hasPhotos"
                          label="Show reviews with photos"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Main Reviews Card Container -->
              <div class="bg-white dark:bg-muted-900 rounded-xl sm:rounded-2xl border border-muted-200 dark:border-muted-800 shadow-sm backdrop-blur-sm dark:backdrop-blur-md overflow-hidden mx-2 sm:mx-0">
                <!-- Card Header -->
                <div class="px-3 sm:px-6 py-3 sm:py-4 border-b border-muted-200 dark:border-muted-800 bg-muted-50/50 dark:bg-muted-800">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <h3 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">User Reviews</h3>
                      <BaseButton
                        size="xs"
                        color="primary"
                        class="h-7 px-3 text-xs"
                        @click="openCreateReviewPanel"
                      >
                        <Icon name="ph:pencil-simple" class="size-3 mr-1.5" />
                        Write Review
                      </BaseButton>
                    </div>
                    <div class="flex items-center gap-1.5 sm:gap-2 text-xs sm:text-sm text-muted-600 dark:text-muted-400">
                      <Icon name="ph:chat-centered-dots" class="size-3.5 sm:size-4" />
                      <span class="hidden xs:inline">{{ filteredReviews.length }} {{ filteredReviews.length === 1 ? 'review' : 'reviews' }}</span>
                      <span class="xs:hidden">{{ filteredReviews.length }}</span>
                    </div>
                  </div>
                </div>

                <!-- Reviews List Content -->
                <div class="p-3 sm:p-6">
                  <!-- AI Review Insights -->
                  <div v-if="filteredReviews.length > 0" class="mb-6">
                    <div class="bg-white dark:bg-muted-900 rounded-xl border border-muted-200 dark:border-muted-700 overflow-hidden">
                      <!-- Header -->
                      <div class="px-4 py-3 border-b border-muted-200 dark:border-muted-700 bg-muted-50/50 dark:bg-muted-800/50">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2.5">
                            <div class="size-8 rounded-lg bg-muted-100 dark:bg-muted-800 flex items-center justify-center">
                              <Icon name="ph:brain-duotone" class="size-4 text-muted-500 dark:text-muted-400" />
                            </div>
                            <div>
                              <h4 class="font-medium text-muted-700 dark:text-muted-300">Review Insights</h4>
                              <p class="text-xs text-muted-500">Analysis summary</p>
                            </div>
                          </div>
                          <div class="flex items-center gap-1.5 px-2 py-1 bg-muted-50 dark:bg-muted-800/50 rounded-full">
                            <div class="size-1.5 rounded-full bg-muted-400"></div>
                            <span class="text-xs font-medium text-muted-500 dark:text-muted-400">AI</span>
                          </div>
                        </div>
                      </div>

                      <!-- Content -->
                      <div class="p-4">
                        <div class="space-y-4">
                          <!-- Summary -->
                          <div class="flex items-start gap-3">
                            <div class="size-6 rounded-full bg-muted-100 dark:bg-muted-800 flex items-center justify-center shrink-0 mt-0.5">
                              <Icon name="ph:quotes-duotone" class="size-3 text-muted-600 dark:text-muted-400" />
                            </div>
                            <div class="flex-1">
                              <p class="text-sm text-muted-700 dark:text-muted-300 leading-relaxed">{{ generateReviewSummary }}</p>
                            </div>
                          </div>

                          <!-- Key Insights Grid -->
                          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 pt-2">
                            <!-- Most Mentioned -->
                            <div class="flex items-center gap-2.5 p-3 bg-muted-50 dark:bg-muted-800/50 rounded-lg">
                              <div class="size-6 rounded-full bg-emerald-100 dark:bg-emerald-500/20 flex items-center justify-center">
                                <Icon name="ph:trend-up" class="size-3 text-emerald-600 dark:text-emerald-400" />
                              </div>
                              <div class="flex-1 min-w-0">
                                <div class="text-xs font-medium text-muted-900 dark:text-white">Most Praised</div>
                                <div class="text-xs text-muted-500 truncate">Wi-Fi & Location</div>
                              </div>
                            </div>

                            <!-- Average Sentiment -->
                            <div class="flex items-center gap-2.5 p-3 bg-muted-50 dark:bg-muted-800/50 rounded-lg">
                              <div class="size-6 rounded-full bg-blue-100 dark:bg-blue-500/20 flex items-center justify-center">
                                <Icon name="ph:smiley" class="size-3 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div class="flex-1 min-w-0">
                                <div class="text-xs font-medium text-muted-900 dark:text-white">Sentiment</div>
                                <div class="text-xs text-muted-500">{{ reviewStats?.averageRating || '4.2' }}/5 Positive</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Individual Review Cards -->
                  <div v-if="paginatedReviews.length > 0" class="space-y-4 sm:space-y-6">
                    <div
                      v-for="review in paginatedReviews"
                      :key="review.id"
                      :class="[
                        'rounded-lg sm:rounded-xl p-3 sm:p-5 border transition-colors relative',
                        review.helpfulVotes >= 15
                          ? 'bg-gradient-to-br from-amber-50/80 to-orange-50/80 dark:from-amber-900/20 dark:to-orange-900/20 border-amber-200/60 dark:border-amber-700/60 hover:border-amber-300 dark:hover:border-amber-600'
                          : 'bg-muted-50/50 dark:bg-muted-800 border-muted-200/50 dark:border-muted-700/50 hover:border-muted-300 dark:hover:border-muted-600'
                      ]"
                    >
                      <!-- Most Liked Badge -->
                      <div v-if="review.helpfulVotes >= 15" class="absolute -top-2 -right-2 bg-gradient-to-r from-amber-400 to-orange-400 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg flex items-center gap-1">
                        <Icon name="ph:crown-simple" class="size-3" />
                        <span class="hidden xs:inline">Most Liked</span>
                      </div>
                      <div class="flex items-start gap-3 sm:gap-4">
                        <!-- User Avatar -->
                        <div class="shrink-0">
                          <div class="size-8 sm:size-10 rounded-full overflow-hidden bg-muted-100 dark:bg-muted-800 ring-1 ring-muted-200 dark:ring-muted-700">
                            <img
                              :src="review.user.photo.path"
                              :alt="review.user.firstName"
                              class="w-full h-full object-cover"
                            />
                          </div>
                        </div>

                        <!-- Review Content -->
                        <div class="flex-1 min-w-0">
                          <!-- Header Section -->
                          <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-3 mb-3">
                            <div class="min-w-0 flex-1">
                              <div class="flex items-center gap-2 mb-1 flex-wrap">
                                <h4 class="font-semibold text-sm sm:text-base text-gray-900 dark:text-white truncate">
                                  {{ review.user.firstName + ' ' + review.user.lastName }}
                                </h4>
                                <!-- Verified Stay Badge -->
                                <div v-if="review.isVerifiedStay" class="inline-flex items-center gap-1 px-1.5 sm:px-2 py-0.5 bg-green-50 dark:bg-green-500/10 text-green-600 dark:text-green-400 text-xs font-medium rounded-full">
                                  <Icon name="ph:check-circle-fill" class="size-2.5 sm:size-3" />
                                  <span class="hidden xs:inline">Verified </span>
                                  <span class="xs:hidden">Verified Stay</span>
                                </div>
                              </div>
                              <div class="flex flex-col xs:flex-row xs:items-center gap-1 xs:gap-2 text-xs text-muted-500">
                                <span class="font-medium text-muted-600 dark:text-muted-400">{{ getBookingContext(review) }}</span>
                                <span class="hidden xs:inline text-muted-400">•</span>
                                <span><span class="hidden sm:inline">Member since </span>{{ format(new Date(review.user.createdAt), 'MMM yyyy') }}</span>
                              </div>
                            </div>
                            <div class="flex items-center gap-1.5 sm:gap-2 shrink-0 self-start">
                              <div class="flex items-center">
                                <Icon
                                  v-for="i in 5"
                                  :key="i"
                                  :name="i <= review.rating ? 'ph:star-fill' : 'ph:star'"
                                  class="size-3 sm:size-3.5 text-amber-400"
                                />
                              </div>
                              <span class="text-xs text-muted-500 font-medium">{{ review.rating }}.0</span>
                            </div>
                          </div>

                          <!-- Review Content -->
                          <div class="mb-3 sm:mb-4">
                            <p class="text-sm text-muted-600 dark:text-muted-400 leading-relaxed mb-3">{{ review.content }}</p>

                            <!-- Review Highlights/Badges -->
                            <div v-if="getReviewHighlights(review).length > 0" class="flex flex-wrap gap-2">
                              <div
                                v-for="highlight in getReviewHighlights(review)"
                                :key="highlight.type"
                                :class="[
                                  'inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border transition-all duration-200 hover:scale-105',
                                  highlight.type === 'most-liked'
                                    ? 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/30 dark:to-pink-900/30 border-red-200 dark:border-red-700 shadow-sm'
                                    : 'bg-white/90 dark:bg-muted-800/90 border-muted-200/60 dark:border-muted-700/60 hover:bg-white dark:hover:bg-muted-800 shadow-sm'
                                ]"
                              >
                                <Icon :name="highlight.icon" :class="['size-3', highlight.color]" />
                                <span class="text-muted-700 dark:text-muted-300">{{ highlight.label }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- Review Images -->
                          <div v-if="review.images && review.images.length > 0" class="mb-3 sm:mb-4">
                            <div class="flex gap-2 overflow-x-auto pb-2 scrollbar-hide -mx-1 px-1">
                              <div
                                v-for="(image, index) in review.images"
                                :key="index"
                                class="shrink-0 size-16 sm:size-20 rounded-lg overflow-hidden border border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600 transition-colors cursor-pointer"
                              >
                                <img
                                  :src="image.path || image"
                                  :alt="`Review photo ${index + 1}`"
                                  class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                                />
                              </div>
                            </div>
                          </div>

                          <!-- Category Ratings -->
                          <div class="mb-3 sm:mb-4 p-2.5 sm:p-3 bg-white/50 dark:bg-muted-900 rounded-lg border border-muted-200/30 dark:border-muted-700/30">
                            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3">
                              <div v-if="review.cleanliness > 0" class="text-center">
                                <div class="text-xs text-muted-500 capitalize mb-1 sm:mb-1.5 font-medium">Cleanliness</div>
                                <div class="flex justify-center gap-0.5">
                                  <Icon
                                    v-for="i in 5"
                                    :key="i"
                                    :name="i <= review.cleanliness ? 'ph:star-fill' : 'ph:star'"
                                    class="size-2.5 sm:size-3 text-amber-400"
                                  />
                                </div>
                              </div>
                              <div v-if="review.location > 0" class="text-center">
                                <div class="text-xs text-muted-500 capitalize mb-1 sm:mb-1.5 font-medium">Location</div>
                                <div class="flex justify-center gap-0.5">
                                  <Icon
                                    v-for="i in 5"
                                    :key="i"
                                    :name="i <= review.location ? 'ph:star-fill' : 'ph:star'"
                                    class="size-2.5 sm:size-3 text-amber-400"
                                  />
                                </div>
                              </div>
                              <div v-if="review.amenities > 0" class="text-center">
                                <div class="text-xs text-muted-500 capitalize mb-1 sm:mb-1.5 font-medium">Amenities</div>
                                <div class="flex justify-center gap-0.5">
                                  <Icon
                                    v-for="i in 5"
                                    :key="i"
                                    :name="i <= review.amenities ? 'ph:star-fill' : 'ph:star'"
                                    class="size-2.5 sm:size-3 text-amber-400"
                                  />
                                </div>
                              </div>
                              <div v-if="review.staff > 0" class="text-center">
                                <div class="text-xs text-muted-500 capitalize mb-1 sm:mb-1.5 font-medium">Staff</div>
                                <div class="flex justify-center gap-0.5">
                                  <Icon
                                    v-for="i in 5"
                                    :key="i"
                                    :name="i <= review.staff ? 'ph:star-fill' : 'ph:star'"
                                    class="size-2.5 sm:size-3 text-amber-400"
                                  />
                                </div>
                              </div>
                              <div v-if="review.valueForMoney > 0" class="text-center">
                                <div class="text-xs text-muted-500 capitalize mb-1 sm:mb-1.5 font-medium">Value</div>
                                <div class="flex justify-center gap-0.5">
                                  <Icon
                                    v-for="i in 5"
                                    :key="i"
                                    :name="i <= review.valueForMoney ? 'ph:star-fill' : 'ph:star'"
                                    class="size-2.5 sm:size-3 text-amber-400"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Footer Actions -->
                          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 pt-3 border-t border-muted-200/50 dark:border-muted-700/50">
                            <div class="flex items-center gap-2 sm:gap-4 flex-wrap">
                              <button
                                @click="markReviewHelpful(review.id)"
                                :disabled="markingHelpful.has(review.id)"
                                :class="[
                                  'flex items-center gap-1.5 text-xs transition-colors px-2 py-1 rounded-md',
                                  markingHelpful.has(review.id)
                                    ? 'text-muted-400 cursor-not-allowed'
                                    : 'text-muted-500 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20'
                                ]"
                              >
                                <Icon
                                  :name="markingHelpful.has(review.id) ? 'ph:circle-notch' : 'ph:thumbs-up'"
                                  :class="[
                                    'size-3.5',
                                    markingHelpful.has(review.id) ? 'animate-spin' : ''
                                  ]"
                                />
                                <span class="hidden xs:inline">
                                  {{ markingHelpful.has(review.id) ? 'Marking...' : `Helpful (${review.helpfulVotes})` }}
                                </span>
                                <span class="xs:hidden">{{ review.helpfulVotes }}</span>
                              </button>
                              <button
                                v-if="userSession.isLoggedIn"
                                @click="toggleReplyForm(review.id)"
                                class="flex items-center gap-1.5 text-xs text-muted-500 hover:text-muted-700 dark:hover:text-muted-300 transition-colors px-2 py-1 rounded-md hover:bg-white/50 dark:hover:bg-muted-800/50"
                                :class="{ 'text-primary-600 dark:text-primary-400': replyingTo === review.id }"
                              >
                                <Icon name="ph:chat-circle" class="size-3.5" />
                                <span class="hidden xs:inline">{{ replyingTo === review.id ? 'Cancel Reply' : 'Reply' }}</span>
                                <span class="xs:hidden">{{ replyingTo === review.id ? 'Cancel' : 'Reply' }}</span>
                              </button>
                              <button
                                v-else
                                @click="$router.push('/auth/')"
                                class="flex items-center gap-1.5 text-xs text-muted-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors px-2 py-1 rounded-md hover:bg-white/50 dark:hover:bg-muted-800/50"
                              >
                                <Icon name="ph:chat-circle" class="size-3.5" />
                                <span>Reply</span>
                              </button>
                              <div class="text-xs text-muted-400">
                                <span class="hidden sm:inline">{{ format(review.createdAt, 'MMM d') }}</span>
                                <span class="sm:hidden">{{ format(review.createdAt, 'MMM d') }}</span>
                              </div>
                            </div>
                            <div class="flex items-center gap-2 sm:gap-3 justify-between sm:justify-end">
                              <!-- Booking Connection -->
                              <div v-if="review.bookingId" class="flex items-center gap-1 text-xs text-muted-400">
                                <Icon name="ph:calendar-check" class="size-3" />
                                <span class="hidden sm:inline">Booking verified</span>
                                <span class="sm:hidden">Verified</span>
                              </div>
                              <button
                                @click="openReportModal(review.id)"
                                class="text-xs text-muted-400 hover:text-danger-500 dark:hover:text-danger-400 transition-colors px-2 py-1 rounded-md hover:bg-danger-50 dark:hover:bg-danger-900/20"
                              >
                                <Icon name="ph:flag" class="size-3 mr-1" />
                                Report
                              </button>
                            </div>
                          </div>

                          <!-- Host Response -->
                          <div
                            v-if="review.hostResponse"
                            class="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-muted-200/50 dark:border-muted-700/50"
                          >
                            <div class="bg-white/60 dark:bg-muted-900 rounded-lg p-3 sm:p-4 border-l-3 border-l-primary-400">
                              <div class="flex items-center gap-2 sm:gap-2.5 mb-2">
                                <div class="size-6 sm:size-7 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
                                  <Icon name="ph:buildings" class="size-3 sm:size-3.5 text-primary-500" />
                                </div>
                                <div>
                                  <div class="font-medium text-sm text-gray-900 dark:text-white">{{ review.space.name }}</div>
                                  <div class="text-xs text-muted-500">
                                    <span class="hidden sm:inline">{{ format(new Date(getReviewTimestamp(review).getTime() + 2 * 24 * 60 * 60 * 1000), 'MMM d, yyyy') }}</span>
                                    <span class="sm:hidden">{{ format(new Date(getReviewTimestamp(review).getTime() + 2 * 24 * 60 * 60 * 1000), 'MMM d') }}</span>
                                  </div>
                                </div>
                              </div>
                              <p class="text-sm text-muted-600 dark:text-muted-400 leading-relaxed">{{ review.hostResponse }}</p>
                            </div>
                          </div>

                          <!-- Review Replies -->
                          <div v-if="review.replies && review.replies.length > 0" class="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-muted-200/50 dark:border-muted-700/50">
                            <div class="space-y-2 sm:space-y-3">
                              <div class="text-xs text-muted-500 font-medium">{{ review.replies.length }} {{ review.replies.length === 1 ? 'Reply' : 'Replies' }}</div>
                              <div v-for="reply in review.replies" :key="reply.id" class="flex items-start gap-2 sm:gap-3 pl-3 sm:pl-4 border-l-2 border-muted-200 dark:border-muted-700">
                                <div class="size-6 sm:size-8 rounded-full overflow-hidden bg-muted-100 dark:bg-muted-800 shrink-0">
                                  <img
                                    :src="reply.user.photo.path"
                                    :alt="reply.user.firstName"
                                    class="w-full h-full object-cover"
                                  />
                                </div>
                                <div class="flex-1 min-w-0">
                                  <div class="flex items-center gap-2 mb-1 flex-wrap">
                                    <span class="text-xs font-medium text-gray-900 dark:text-white truncate">{{ reply.user.firstName + ' ' + reply.user.lastName }}</span>
                                    <span class="text-xs text-muted-500">{{ format(new Date(getReviewTimestamp(review).getTime() + Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000), 'MMM d') }}</span>
                                  </div>
                                  <p class="text-sm text-muted-600 dark:text-muted-400 leading-relaxed">{{ reply.content }}</p>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Reply Form -->
                          <div v-if="replyingTo === review.id" class="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-muted-200/50 dark:border-muted-700/50">
                            <div class="bg-white/60 dark:bg-muted-900 rounded-lg p-3 sm:p-4 border border-muted-200/50 dark:border-muted-700/50">
                              <div class="flex items-start gap-2 sm:gap-3">
                                <!-- User Avatar -->
                                <div class="shrink-0">
                                  <div class="size-6 sm:size-8 rounded-full overflow-hidden bg-muted-100 dark:bg-muted-800 ring-1 ring-muted-200 dark:ring-muted-700">
                                    <img
                                      :src="userSession.user?.photo?.path || '/img/avatars/default-user.jpg'"
                                      :alt="userSession.user?.firstName || 'User'"
                                      class="w-full h-full object-cover"
                                    />
                                  </div>
                                </div>

                                <!-- Reply Form Content -->
                                <div class="flex-1 space-y-2 sm:space-y-3">
                                  <div class="text-xs text-muted-600 dark:text-muted-400">
                                    Replying to <span class="font-medium">{{ review.user.firstName }}</span>
                                  </div>

                                  <!-- Reply Textarea -->
                                  <div class="relative">
                                    <textarea
                                      v-model="replyContent"
                                      :maxlength="maxReplyLength"
                                      placeholder="Write your reply..."
                                      :rows="3"
                                      class="w-full px-3 py-2 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 rounded-lg text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                      :class="{ 'border-red-300 dark:border-red-600': replyContent.length > maxReplyLength }"
                                    ></textarea>
                                    <!-- Character Counter -->
                                    <div class="absolute bottom-2 right-2 text-xs text-muted-400">
                                      {{ replyContent.length }}/{{ maxReplyLength }}
                                    </div>
                                  </div>

                                  <!-- Reply Actions -->
                                  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                                    <div class="flex flex-col xs:flex-row xs:items-center gap-2 xs:gap-4">
                                      <div class="text-xs text-muted-500">
                                        <Icon name="ph:info" class="size-3 inline mr-1" />
                                        <span class="hidden xs:inline">Be respectful and constructive</span>
                                        <span class="xs:hidden">Be respectful</span>
                                      </div>
                                      <div v-if="replyContent.length > maxReplyLength" class="text-xs text-red-500">
                                        <Icon name="ph:warning" class="size-3 inline mr-1" />
                                        Reply too long
                                      </div>
                                      <div v-else-if="replyContent.length > 0 && replyContent.length < 10" class="text-xs text-amber-500">
                                        <Icon name="ph:info" class="size-3 inline mr-1" />
                                        <span class="hidden xs:inline">Minimum 10 characters</span>
                                        <span class="xs:hidden">Min 10 chars</span>
                                      </div>
                                    </div>
                                    <div class="flex items-center gap-2 justify-end">
                                      <button
                                        @click="cancelReply"
                                        class="px-3 py-1.5 text-xs font-medium text-muted-600 dark:text-muted-400 hover:text-muted-800 dark:hover:text-muted-200 transition-colors"
                                      >
                                        Cancel
                                      </button>
                                      <button
                                        @click="submitReply(review.id)"
                                        :disabled="!replyContent.trim() || isSubmittingReply || replyContent.length > maxReplyLength || replyContent.length < 10"
                                        class="px-3 sm:px-4 py-1.5 text-xs font-medium bg-primary-500 hover:bg-primary-600 disabled:bg-muted-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-1.5"
                                      >
                                        <Icon v-if="isSubmittingReply" name="ph:spinner" class="size-3 animate-spin" />
                                        <Icon v-else name="ph:paper-plane-tilt" class="size-3" />
                                        <span class="hidden xs:inline">{{ isSubmittingReply ? 'Posting...' : 'Post Reply' }}</span>
                                        <span class="xs:hidden">{{ isSubmittingReply ? 'Posting...' : 'Post' }}</span>
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- No Reviews Message -->
                  <div v-else class="text-center py-8 sm:py-12 px-4">
                    <Icon name="ph:chat-centered-dots" class="size-12 sm:size-16 text-muted-400 mx-auto mb-3 sm:mb-4" />
                    <h4 class="font-medium text-base sm:text-lg mb-2 text-gray-900 dark:text-white">No reviews match your filters</h4>
                    <p class="text-sm sm:text-base text-muted-500 mb-4 sm:mb-6">Try adjusting your filter settings to see more reviews</p>
                    <BaseButton
                      variant="outline"
                      color="muted"
                      size="sm"
                      @click="reviewFilters.rating = 'all'; reviewFilters.hasPhotos = false;"
                    >
                      <Icon name="ph:funnel-x" class="size-3 sm:size-4 mr-1.5 sm:mr-2" />
                      Clear Filters
                    </BaseButton>
                  </div>
                </div>

                <!-- Pagination Footer -->
                <div v-if="filteredReviews.length > reviewsPerPage" class="px-3 sm:px-4 py-2.5 sm:py-3 border-t border-muted-200/50 dark:border-muted-700/50 bg-muted-25 dark:bg-muted-850">
                  <div class="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-2 xs:gap-0">
                    <!-- Results Info -->
                    <div class="text-xs text-muted-500 dark:text-muted-400 font-medium text-center xs:text-left">
                      <span class="hidden sm:inline">Showing </span>{{ (currentReviewPage - 1) * reviewsPerPage + 1 }}-{{ Math.min(currentReviewPage * reviewsPerPage, filteredReviews.length) }} of {{ filteredReviews.length }}
                    </div>

                    <!-- Compact Pagination -->
                    <div class="flex items-center justify-center xs:justify-end gap-0.5 sm:gap-1">
                      <!-- Previous Button -->
                      <button
                        @click="currentReviewPage = currentReviewPage - 1"
                        :disabled="currentReviewPage <= 1"
                        class="p-1 sm:p-1.5 rounded-md text-muted-500 hover:text-muted-700 dark:hover:text-muted-300 hover:bg-muted-100 dark:hover:bg-muted-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Icon name="ph:caret-left" class="size-3.5 sm:size-4" />
                      </button>

                      <!-- Page Numbers -->
                      <div class="flex items-center gap-0.5">
                        <template v-for="page in getVisiblePages()" :key="page">
                          <button
                            v-if="page !== '...'"
                            @click="currentReviewPage = page"
                            :class="[
                              'px-2 sm:px-2.5 py-1 text-xs font-medium rounded-md transition-colors min-w-[28px] sm:min-w-[32px]',
                              page === currentReviewPage
                                ? 'bg-primary-500 text-white'
                                : 'text-muted-600 dark:text-muted-400 hover:text-muted-800 dark:hover:text-muted-200 hover:bg-muted-100 dark:hover:bg-muted-800'
                            ]"
                          >
                            {{ page }}
                          </button>
                          <span v-else class="px-0.5 sm:px-1 text-xs text-muted-400">...</span>
                        </template>
                      </div>

                      <!-- Next Button -->
                      <button
                        @click="currentReviewPage = currentReviewPage + 1"
                        :disabled="currentReviewPage >= totalReviewPages"
                        class="p-1 sm:p-1.5 rounded-md text-muted-500 hover:text-muted-700 dark:hover:text-muted-300 hover:bg-muted-100 dark:hover:bg-muted-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Icon name="ph:caret-right" class="size-3.5 sm:size-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
<!-- Share Your Experience - Clean & Compact -->
<div class="bg-white dark:bg-muted-900 rounded-lg p-4 border border-muted-200 dark:border-muted-700">
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="size-9 rounded-lg bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center">
        <Icon name="ph:star-duotone" class="size-4 text-primary-500" />
      </div>
      <div>
        <h3 class="font-medium text-muted-900 dark:text-white">Share Your Experience</h3>
        <p class="text-sm text-muted-500">Help others choose the right workspace</p>
      </div>
    </div>

    <div class="flex items-center gap-2 shrink-0">
      <BaseButton
        color="primary"
        class="h-9 px-4 text-sm"
        @click="openCreateReviewPanel"
      >
        <Icon name="ph:pencil-simple" class="size-4 mr-2" />
        Write Review
      </BaseButton>

      <BaseButton
        variant="ghost"
        color="muted"
        class="h-9 px-3"
        @click="toggleReviewGuidelines"
      >
        <Icon name="ph:info" class="size-4 mr-1.5" />
        <span class="text-sm font-medium">Guidelines</span>
      </BaseButton>
    </div>
  </div>

  <!-- Comprehensive Guidelines -->
  <div v-if="showGuidelinesExpanded" class="pt-3 mt-3 border-t border-muted-200 dark:border-muted-700">
    <div class="space-y-4">
      <!-- Do's Section -->
      <div>
        <h5 class="text-sm font-semibold text-muted-900 dark:text-white mb-2 flex items-center gap-1.5">
          <Icon name="ph:check-circle-fill" class="size-4 text-primary-500" />
          What to Include
        </h5>
        <div class="space-y-1.5 text-sm text-muted-600 dark:text-muted-400">
          <div class="flex items-start gap-2">
            <Icon name="ph:check" class="size-3 text-muted-500 dark:text-muted-400 mt-1 shrink-0" />
            <span>Your personal experience and specific details</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:check" class="size-3 text-muted-500 dark:text-muted-400 mt-1 shrink-0" />
            <span>Honest ratings for cleanliness, amenities, and location</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:check" class="size-3 text-muted-500 dark:text-muted-400 mt-1 shrink-0" />
            <span>Photos of the workspace and facilities</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:check" class="size-3 text-muted-500 dark:text-muted-400 mt-1 shrink-0" />
            <span>Wi-Fi speed, noise levels, and meeting room quality</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:check" class="size-3 text-muted-500 dark:text-muted-400 mt-1 shrink-0" />
            <span>Parking availability and accessibility features</span>
          </div>
        </div>
      </div>

      <!-- Don'ts Section -->
      <div>
        <h5 class="text-sm font-semibold text-muted-900 dark:text-white mb-2 flex items-center gap-1.5">
          <Icon name="ph:x-circle-fill" class="size-4 text-danger-500" />
          What to Avoid
        </h5>
        <div class="space-y-1.5 text-sm text-muted-600 dark:text-muted-400">
          <div class="flex items-start gap-2">
            <Icon name="ph:x" class="size-3 text-danger-500 mt-1 shrink-0" />
            <span>Inappropriate language or personal attacks</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:x" class="size-3 text-danger-500 mt-1 shrink-0" />
            <span>Fake reviews or promotional content</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:x" class="size-3 text-danger-500 mt-1 shrink-0" />
            <span>Personal information about staff or other users</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:x" class="size-3 text-danger-500 mt-1 shrink-0" />
            <span>Reviews based on hearsay or second-hand information</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:x" class="size-3 text-danger-500 mt-1 shrink-0" />
            <span>Competitor comparisons or business promotions</span>
          </div>
        </div>
      </div>

      <!-- Tips Section -->
      <div class="bg-muted-50 dark:bg-muted-800/50 rounded-lg p-3">
        <h5 class="text-sm font-semibold text-muted-900 dark:text-white mb-2 flex items-center gap-1.5">
          <Icon name="ph:lightbulb-duotone" class="size-4 text-warning-500" />
          Pro Tips
        </h5>
        <div class="space-y-1.5 text-sm text-muted-600 dark:text-muted-400">
          <div class="flex items-start gap-2">
            <Icon name="ph:star" class="size-3 text-warning-500 mt-1 shrink-0" />
            <span>Be specific about your work setup and needs</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:star" class="size-3 text-warning-500 mt-1 shrink-0" />
            <span>Mention peak hours and crowd levels</span>
          </div>
          <div class="flex items-start gap-2">
            <Icon name="ph:star" class="size-3 text-warning-500 mt-1 shrink-0" />
            <span>Include booking process and staff helpfulness</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
            </div>

            <!-- Quote Tab -->
            <div v-if="activeTab === 'quote'" class="space-y-8">
              <BaseCard rounded="lg" class="relative overflow-hidden">
                <div class="absolute -right-16 -top-16 opacity-5 dark:opacity-[0.03] pointer-events-none">
                  <Icon name="ph:handshake-duotone" class="size-72 text-primary-500" />
                </div>
                
                <div class="p-6 relative">
                  <!-- Header -->
                  <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-6 mb-6 border-b border-muted-200 dark:border-muted-800">
                    <div class="flex items-center gap-4">
                      <div class="size-12 rounded-xl bg-primary-500/10 dark:bg-primary-500/20 flex items-center justify-center">
                        <Icon name="ph:handshake-duotone" class="size-6 text-primary-500" />
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold">Request a Quote</h3>
                        <p class="text-sm text-muted-500 mt-1">Get a custom pricing for this workspace</p>
                      </div>
                    </div>
                    
                    <div class="flex items-center gap-2">
                      <BaseTag color="warning" size="sm" class="uppercase tracking-wider">
                        <Icon name="ph:clock-countdown" class="size-4 mr-1" />
                        <span>24-48h response</span>
                      </BaseTag>
                    </div>
                  </div>
                  
                  <!-- Quote Request Form -->
                  <div class="bg-white dark:bg-muted-900 rounded-xl border border-muted-200 dark:border-muted-700 p-6">
                    <form @submit.prevent="submitQuoteRequest" class="space-y-6">
                      <!-- Room Selection -->
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                          <BaseSelect
                            id="room-select"
                            label="Room Type"
                            v-model="quoteForm.roomId"
                            placeholder="Select a room"
                            :classes="{
                              wrapper: 'w-full',
                              select: 'w-full h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <option disabled value="">Select a room</option>
                            <option v-for="room in spaceData.space.rooms" :key="room.id" :value="room.id">
                              {{ room.name }} ({{ room.capacity }} people)
                            </option>
                          </BaseSelect>
                        </div>
                        
                        <div class="space-y-2">
                          <BaseInputNumber
                            label="Number of People"
                            id="people-input"
                            v-model="quoteForm.numberOfPeople"
                            min="1"
                            placeholder="How many people?"
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                          </BaseInputNumber>
                        </div>
                      </div>
                      
                      <!-- Duration Section -->
                      <div class="space-y-4">
                        <div class="flex items-center gap-2">
                          <Icon name="ph:calendar-check-duotone" class="size-5 text-primary-500" />
                          <h4 class="font-medium">Duration <span class="text-danger-500">*</span></h4>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <!-- Date Selection -->
                          <div class="space-y-2">
                            <div 
                              id="start-date"
                              @click="isQuoteStartDateOpen = true"
                              class="w-full px-4 h-11 rounded-lg border border-muted-300 dark:border-muted-700 bg-white dark:bg-muted-900 hover:border-muted-400 cursor-pointer flex items-center justify-between"
                            >
                              <span :class="quoteForm.startDate ? 'text-muted-900 dark:text-white' : 'text-muted-400'">
                                {{ formatDate(quoteForm.startDate) }}
                              </span>
                              <Icon name="ph:calendar" class="size-5 text-muted-400" />
                            </div>
                            <div v-if="isQuoteStartDateOpen" class="relative mt-2 z-30">
                              <div class="absolute bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded-lg shadow-lg p-2">
                                <div class="flex justify-between items-center p-2">
                                  <h5 class="font-medium">Select start date</h5>
                                  <button type="button" @click="isQuoteStartDateOpen = false" class="text-muted-400 hover:text-muted-600">
                                    <Icon name="ph:x" class="size-5" />
                                  </button>
                                </div>
                                <Calendar 
                                  v-model="quoteForm.startDate" 
                                  :min-date="new Date()" 
                                  @dayclick="handleQuoteStartDateSelect"
                                  is-expanded
                                  trim-weeks
                                  :attributes="quoteCalendarAttrs"
                                  class="calendar-sm"
                                />
                              </div>
                            </div>
                          </div>
                          
                          <div class="space-y-2">
                            <div 
                              id="end-date"
                              @click="isQuoteEndDateOpen = true"
                              class="w-full px-4 h-11 rounded-lg border border-muted-300 dark:border-muted-700 bg-white dark:bg-muted-900 hover:border-muted-400 cursor-pointer flex items-center justify-between"
                            >
                              <span :class="quoteForm.endDate ? 'text-muted-900 dark:text-white' : 'text-muted-400'">
                                {{ formatDate(quoteForm.endDate) }}
                              </span>
                              <Icon name="ph:calendar" class="size-5 text-muted-400" />
                            </div>
                            <div v-if="isQuoteEndDateOpen" class="relative mt-2 z-30">
                              <div class="absolute bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded-lg shadow-lg p-2">
                                <div class="flex justify-between items-center p-2">
                                  <h5 class="font-medium">Select end date</h5>
                                  <button type="button" @click="isQuoteEndDateOpen = false" class="text-muted-400 hover:text-muted-600">
                                    <Icon name="ph:x" class="size-5" />
                                  </button>
                                </div>
                                <Calendar 
                                  v-model="quoteForm.endDate" 
                                  :min-date="quoteForm.startDate || new Date()" 
                                  @dayclick="handleQuoteEndDateSelect"
                                  is-expanded
                                  trim-weeks
                                  :attributes="quoteCalendarAttrs"
                                  class="calendar-sm"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Pricing & Contact Information -->
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                          <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <span class="text-muted-400">{{ quoteForm.currency }}</span>
                            </div>
                            <BaseInputNumber

                            label="Expected Budget (Optional)"
                              id="price-input"
                              v-model="quoteForm.price"
                              min="0"
                              placeholder="Your budget" 
                              :classes="{
                                wrapper: 'w-full',
                                input: 'pl-14 h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                              }"
                            >
                              <template #suffix>
                                <Icon name="ph:currency-circle-duotone" class="size-5 text-muted-400" />
                              </template>
                            </BaseInputNumber>
                          </div>
                        </div>
                        
                        <div class="space-y-2">
                          <!-- Show contact info field for logged-in users -->
                          <BaseInput
                            v-if="userSession?.user?.id"
                            label="Additional Contact Information (Optional)"
                            id="contact-input"
                            v-model="quoteForm.contactInfo"
                            type="text"
                            placeholder="Phone number or additional contact info"
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <template #suffix>
                              <Icon name="ph:phone-duotone" class="size-5 text-muted-400" />
                            </template>
                          </BaseInput>

                          <!-- Show guest email field for non-logged-in users -->
                          <BaseInput
                            v-else
                            label="Email Address *"
                            id="guest-email-input"
                            v-model="quoteForm.guestEmail"
                            type="email"
                            placeholder="<EMAIL>"
                            required
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <template #suffix>
                              <Icon name="ph:envelope-duotone" class="size-5 text-muted-400" />
                            </template>
                          </BaseInput>
                        </div>
                      </div>

                      <!-- Guest Contact Information Notice -->
                      <div v-if="!userSession?.user?.id" class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4 mb-6">
                        <div class="flex items-start gap-3">
                          <Icon name="ph:info-duotone" class="size-5 text-primary-600 dark:text-primary-400 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 class="text-sm font-medium text-primary-900 dark:text-primary-100 mb-1">
                              Contact Information Required
                            </h4>
                            <p class="text-sm text-primary-700 dark:text-primary-300">
                              Please provide your contact details so we can send you the quote and follow up on your request.
                            </p>
                          </div>
                        </div>
                      </div>

                      <!-- Guest Name Fields (for non-logged-in users) -->
                      <div v-if="!userSession?.user?.id" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                          <BaseInput
                            label="First Name *"
                            id="guest-first-name-input"
                            v-model="quoteForm.guestFirstName"
                            type="text"
                            placeholder="Your first name"
                            required
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <template #suffix>
                              <Icon name="ph:user-duotone" class="size-5 text-muted-400" />
                            </template>
                          </BaseInput>
                        </div>

                        <div class="space-y-2">
                          <BaseInput
                            label="Last Name *"
                            id="guest-last-name-input"
                            v-model="quoteForm.guestLastName"
                            type="text"
                            placeholder="Your last name"
                            required
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <template #suffix>
                              <Icon name="ph:user-duotone" class="size-5 text-muted-400" />
                            </template>
                          </BaseInput>
                        </div>
                      </div>

                      <!-- Notes -->
                      <div class="space-y-2">
                        <BaseTextarea
                          label="Additional Notes"
                          id="notes-textarea" 
                          v-model="quoteForm.notes" 
                          rows="4" 
                          placeholder="Include any specific requirements or questions..." 
                          :classes="{
                            textarea: 'bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                          }"
                        />
                      </div>
                      
                      <!-- Terms -->
                      <div class="flex items-start gap-3 pt-2">
                        <div class="flex items-center h-5 mt-0.5">
                          <BaseCheckbox
                            id="quote-terms"
                            v-model="quoteForm.termsAccepted"
                            name="terms"
                            :classes="{
                              wrapper: '',
                              input: 'text-primary-500 border-muted-300 dark:border-muted-700 rounded'
                            }"
                          />
                        </div>
                        <label for="quote-terms" class="text-sm text-muted-600 dark:text-muted-400 cursor-pointer">
                          I understand that by submitting this form, I am requesting a quote and agree to be contacted regarding my inquiry. My information will be handled in accordance with the privacy policy.
                        </label>
                      </div>
                      
                      <!-- Submit Button and Status -->
                      <div class="pt-4">
                        <div class="flex flex-col sm:flex-row items-center gap-4">
                          <BaseButton
                            type="submit"
                            color="primary"
                            size="lg"
                            :disabled="quoteForm.isSubmitting || !isValidQuoteForm"
                            :loading="quoteForm.isSubmitting"
                            class="w-full sm:w-auto h-11 px-8"
                          >
                            <div class="flex items-center gap-2">
                              <Icon name="ph:handshake-bold" class="size-5" />
                              <span>Submit Quote Request</span>
                            </div>
                          </BaseButton>
                          
                          <transition 
                            enter-active-class="transition duration-100 ease-out" 
                            enter-from-class="transform scale-95 opacity-0" 
                            enter-to-class="transform scale-100 opacity-100" 
                            leave-active-class="transition duration-75 ease-in" 
                            leave-from-class="transform scale-100 opacity-100" 
                            leave-to-class="transform scale-95 opacity-0"
                            v-if="quoteForm.success"
                          >
                            <BaseTag color="success" size="md" class="h-11 px-4">
                              <div class="flex items-center gap-2">
                                <Icon name="ph:check-circle" class="size-5" />
                                <span>{{ quoteForm.success }}</span>
                              </div>
                            </BaseTag>
                          </transition>
                          
                          <transition 
                            enter-active-class="transition duration-100 ease-out" 
                            enter-from-class="transform scale-95 opacity-0" 
                            enter-to-class="transform scale-100 opacity-100" 
                            leave-active-class="transition duration-75 ease-in" 
                            leave-from-class="transform scale-100 opacity-100" 
                            leave-to-class="transform scale-95 opacity-0"
                            v-if="quoteForm.error"
                          >
                            <BaseTag color="danger" size="md" class="h-11 px-4">
                              <div class="flex items-center gap-2">
                                <Icon name="ph:warning-circle" class="size-5" />
                                <span>{{ quoteForm.error }}</span>
                              </div>
                            </BaseTag>
                          </transition>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </BaseCard>
              
              <!-- Quote Process Information -->
              <BaseCard rounded="lg" class="mb-8">
                <div class="p-6 relative overflow-hidden">
                  <div class="absolute -right-10 -top-10 opacity-5 dark:opacity-[0.03]">
                    <Icon name="ph:info-duotone" class="size-48 text-primary-500" />
                  </div>
                  
                  <div class="flex items-center gap-3 mb-6 relative">
                    <div class="size-10 rounded-lg bg-primary-50 dark:bg-primary-500/20 flex items-center justify-center">
                      <Icon name="ph:info-duotone" class="size-5 text-primary-500" />
                    </div>
                    <h3 class="text-lg font-semibold">How It Works</h3>
                  </div>

                  <div class="grid md:grid-cols-3 gap-6">
                    <div class="relative bg-white dark:bg-muted-900 rounded-xl p-5 border border-muted-200 dark:border-muted-700 flex flex-col overflow-hidden">
                      <div class="absolute -right-4 -top-4 opacity-10">
                        <Icon name="ph:note-pencil-duotone" class="size-16 text-primary-500" />
                      </div>
                      <div class="size-10 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center mb-4 text-primary-600 dark:text-primary-400 font-bold">
                        1
                      </div>
                      <h4 class="font-medium mb-2">Submit Request</h4>
                      <p class="text-sm text-muted-500 flex-grow">Fill out the form with your workspace requirements and preferences.</p>
                    </div>
                    
                    <div class="relative bg-white dark:bg-muted-900 rounded-xl p-5 border border-muted-200 dark:border-muted-700 flex flex-col overflow-hidden">
                      <div class="absolute -right-4 -top-4 opacity-10">
                        <Icon name="ph:file-doc-duotone" class="size-16 text-primary-500" />
                      </div>
                      <div class="size-10 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center mb-4 text-primary-600 dark:text-primary-400 font-bold">
                        2
                      </div>
                      <h4 class="font-medium mb-2">Review Quote</h4>
                      <p class="text-sm text-muted-500 flex-grow">Our team will prepare a customized quote based on your needs within 24-48 hours.</p>
                    </div>
                    
                    <div class="relative bg-white dark:bg-muted-900 rounded-xl p-5 border border-muted-200 dark:border-muted-700 flex flex-col overflow-hidden">
                      <div class="absolute -right-4 -top-4 opacity-10">
                        <Icon name="ph:check-square-offset-duotone" class="size-16 text-primary-500" />
                      </div>
                      <div class="size-10 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center mb-4 text-primary-600 dark:text-primary-400 font-bold">
                        3
                      </div>
                      <h4 class="font-medium mb-2">Confirm Booking</h4>
                      <p class="text-sm text-muted-500 flex-grow">Accept the quote to proceed with booking or request adjustments as needed.</p>
                    </div>
                  </div>
                </div>
              </BaseCard>
            </div>

            <!-- Tour Form -->
            <div v-if="activeTab === 'tour'" class="space-y-8">
              <BaseCard rounded="lg" class="relative overflow-hidden">
                <div class="absolute -right-16 -top-16 opacity-5 dark:opacity-[0.03] pointer-events-none">
                  <Icon name="ph:buildings-duotone" class="size-72 text-primary-500" />
                </div>
                
                <div class="p-6 relative">
                  <!-- Header -->
                  <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pb-6 mb-6 border-b border-muted-200 dark:border-muted-800">
                    <div class="flex items-center gap-4">
                      <div class="size-12 rounded-xl bg-primary-500/10 dark:bg-primary-500/20 flex items-center justify-center">
                        <Icon name="ph:buildings-duotone" class="size-6 text-primary-500" />
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold">Schedule a Tour</h3>
                        <p class="text-sm text-muted-500 mt-1">Visit this workspace before making a decision</p>
                      </div>
                    </div>
                    
                    <div class="flex items-center gap-2">
                      <BaseTag color="success" size="sm" class="uppercase tracking-wider">
                        <Icon name="ph:calendar-check" class="size-4 mr-1" />
                        <span>Book today</span>
                      </BaseTag>
                    </div>
                  </div>
                  
                  <!-- Tour Request Form -->
                  <div class="bg-white dark:bg-muted-900 rounded-xl border border-muted-200 dark:border-muted-700 p-6">
                    <form @submit.prevent="submitTour" class="space-y-6">
                      <!-- Tour Type & Number of People -->
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-2">
                          <BaseSelect
                            label="Tour Type"
                            id="tour-type-select"
                            v-model="tourForm.tourType"
                            :classes="{
                              wrapper: 'w-full',
                              select: 'w-full h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <option value="in_person">In Person</option>
                            <option value="virtual">Virtual</option>
                          </BaseSelect>
                        </div>
                        
                        <div class="space-y-2">
                          <BaseInputNumber
                            label="Number of People"
                            id="tour-people-input"
                            v-model="tourForm.numberOfPeople"
                            min="1"
                            max="20"
                            placeholder="How many people?"
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                          </BaseInputNumber>
                        </div>
                      </div>
                      
                      <!-- Date and Time Section -->
                      <div class="space-y-4">
                        <div class="flex items-center gap-2">
                          <Icon name="ph:calendar-check-duotone" class="size-5 text-primary-500" />
                          <h4 class="font-medium">Tour Date & Time <span class="text-danger-500">*</span></h4>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <!-- Date Selection -->
                          <div class="space-y-2">
                            <div 
                              id="tour-date"
                              @click="isTourDateOpen = true"
                              class="w-full px-4 h-11 rounded-lg border border-muted-300 dark:border-muted-700 bg-white dark:bg-muted-900 hover:border-muted-400 cursor-pointer flex items-center justify-between"
                            >
                              <span :class="tourForm.date ? 'text-muted-900 dark:text-white' : 'text-muted-400'">
                                {{ formatDate(tourForm.date) || 'Select date' }}
                              </span>
                              <Icon name="ph:calendar" class="size-5 text-muted-400" />
                            </div>
                            <div v-if="isTourDateOpen" class="relative mt-2 z-30">
                              <div class="absolute bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded-lg shadow-lg p-2">
                                <div class="flex justify-between items-center p-2">
                                  <h5 class="font-medium">Select tour date</h5>
                                  <button type="button" @click="isTourDateOpen = false" class="text-muted-400 hover:text-muted-600">
                                    <Icon name="ph:x" class="size-5" />
                                  </button>
                                </div>
                                <Calendar 
                                  v-model="tourForm.date" 
                                  :min-date="new Date()" 
                                  @dayclick="handleTourDateSelect"
                                  is-expanded
                                  trim-weeks
                                  :attributes="tourCalendarAttrs"
                                  class="calendar-sm"
                                />
                              </div>
                            </div>
                          </div>
                          
                          <!-- Time Selection -->
                          <div class="space-y-2">
                            <BaseSelect
                              label="Tour Time"
                              id="tour-time"
                              v-model="tourForm.time"
                              placeholder="Select time"
                              :classes="{
                                wrapper: 'w-full',
                                select: 'w-full h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                              }"
                            >
                              <option value="09:00">9:00 AM</option>
                              <option value="10:00">10:00 AM</option>
                              <option value="11:00">11:00 AM</option>
                              <option value="12:00">12:00 PM</option>
                              <option value="13:00">1:00 PM</option>
                              <option value="14:00">2:00 PM</option>
                              <option value="15:00">3:00 PM</option>
                              <option value="16:00">4:00 PM</option>
                            </BaseSelect>
                          </div>
                        </div>
                        
                        <!-- Duration -->
                        <div class="space-y-2">
                          <BaseInputNumber
                            label="Duration (minutes)"
                            id="tour-duration-input"
                            v-model="tourForm.duration"
                            min="15"
                            max="120"
                            placeholder="Tour duration in minutes"
                            :classes="{
                              wrapper: 'w-full',
                              input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                          </BaseInputNumber>
                        </div>
                      </div>
                      
                      <!-- Contact Information -->
                      <div class="space-y-4">
                        <div class="flex items-center gap-2">
                          <Icon name="ph:user-circle-duotone" class="size-5 text-primary-500" />
                          <h4 class="font-medium">Contact Information <span class="text-danger-500">*</span></h4>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div class="space-y-2">
                            <BaseInput
                              label="Email Address"
                              id="tour-email"
                              v-model="tourForm.email" 
                              type="email" 
                              placeholder="Your email address"
                              :classes="{
                                wrapper: 'w-full',
                                input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                              }"
                            >
                              <template #suffix>
                                <Icon name="ph:envelope-duotone" class="size-5 text-muted-400" />
                              </template>
                            </BaseInput>
                          </div>
                          
                          <div class="space-y-2">
                            <BaseInput
                              label="Phone Number"
                              id="tour-phone"
                              v-model="tourForm.phoneNumber" 
                              type="tel" 
                              placeholder="Your phone number"
                              :classes="{
                                wrapper: 'w-full',
                                input: 'h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                              }"
                            >
                              <template #suffix>
                                <Icon name="ph:phone-duotone" class="size-5 text-muted-400" />
                              </template>
                            </BaseInput>
                          </div>
                        </div>
                        
                        <div class="space-y-2">
                          <BaseSelect
                            label="Preferred Contact Method"
                            id="tour-contact-method"
                            v-model="tourForm.preferredContactMethod"
                            :classes="{
                              wrapper: 'w-full',
                              select: 'w-full h-11 bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          >
                            <option value="email">Email</option>
                            <option value="phone">Phone</option>
                            <option value="both">WhatsApp</option>
                          </BaseSelect>
                        </div>
                      </div>
                      
                      <!-- Purpose and Requirements -->
                      <div class="space-y-4">
                        <div class="flex items-center gap-2">
                          <Icon name="ph:note-pencil-duotone" class="size-5 text-primary-500" />
                          <h4 class="font-medium">Additional Information</h4>
                        </div>
                        
                        <!-- Purpose -->
                        <div class="space-y-2">
                          <BaseTextarea
                            label="Purpose of Visit"
                            id="tour-purpose"
                            v-model="tourForm.purpose" 
                            rows="3" 
                            placeholder="Please describe the purpose of your visit and what you're looking for..." 
                            :classes="{
                              textarea: 'bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          />
                        </div>
                        
                        <!-- Special Requirements -->
                        <div class="space-y-2">
                          <BaseTextarea
                            label="Special Requirements"
                            id="tour-requirements"
                            v-model="tourForm.specialRequirements" 
                            rows="3" 
                            placeholder="Please specify any special requirements or accommodations needed..." 
                            :classes="{
                              textarea: 'bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-primary-500 dark:focus:border-primary-500 rounded-lg text-sm font-sans'
                            }"
                          />
                        </div>
                      </div>
                      
                      <!-- Terms -->
                      <div class="flex items-start gap-3 pt-2">
                        <div class="flex items-center h-5 mt-0.5">
                          <BaseCheckbox
                            id="tour-terms"
                            v-model="tourForm.termsAccepted"
                            name="terms"
                            :classes="{
                              wrapper: 'h-5 w-5',
                            }"
                          />
                        </div>
                        <div>
                          <label for="tour-terms" class="text-sm text-muted-700 dark:text-muted-300">
                            I agree to the <NuxtLink to="#" class="text-primary-500 hover:underline">Terms & Conditions</NuxtLink> and confirm that I can be contacted regarding this tour request.
                          </label>
                        </div>
                      </div>
                      
                      <!-- Submit Button -->
                      <div class="flex justify-end pt-2">
                        <BaseButton
                          type="submit"
                          color="primary"
                          class="w-full sm:w-auto"
                          :loading="isSubmitting"
                          :disabled="!tourForm.termsAccepted"
                        >
                          <Icon name="ph:calendar-check-duotone" class="size-5 mr-1" />
                          <span>Schedule Tour</span>
                        </BaseButton>
                      </div>
                    </form>
                  </div>
                </div>
              </BaseCard>
            </div>
          </div>

          <!-- Right Column - Booking Card -->
          <div class="lg:col-span-1">
            <!-- Existing Booking Card -->
            <BaseCard ref="bookingRef" color="default" rounded="lg" class="sticky top-24">
              <div class="p-6 space-y-6">
                <!-- Price Header -->
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-2xl font-semibold">
                      {{ formatPrice(bookingType === 'hourly' ? 
                        (selectedRoom?.pricePerHour || spaceData.space.startingFromPriceHourly) : 
                        (selectedRoom?.pricePerDay || spaceData.space.startingFromPrice)) 
                      }}
                      <span class="text-sm font-normal text-muted-500">/ {{ bookingType === 'hourly' ? 'hour' : 'day' }}</span>
                    </div>
                    <div class="text-sm text-muted-500 mt-1 flex items-center gap-2">
                      <span>
                        {{ bookingType === 'hourly' ? 
                          `${formatPrice(selectedRoom?.pricePerDay || spaceData.space.startingFromPrice)} / day` : 
                          `${formatPrice(selectedRoom?.pricePerHour || spaceData.space.startingFromPriceHourly)} / hour` 
                        }}
                      </span>
                      <span class="size-1 rounded-full bg-muted-300 dark:bg-muted-700"></span>
                      <div class="flex items-center gap-1">
                        <Icon name="ph:star-fill" class="text-amber-500 size-4" />
                        <span class="font-medium">{{ spaceData.space.rating || '0.0' }}</span>
                        <span class="text-muted-500">·</span>
                        <span>{{ spaceData.space.totalReviews || 0 }} reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Booking sections -->
                <div class="space-y-6">
                  <!-- Booking Type Toggle -->
                  <div>
                    <label class="block text-sm font-medium mb-3">Choose your booking preference</label>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <BaseRadioHeadless
                        v-model="bookingType"
                        name="booking_type"
                        value="daily"
                      >
                        <BaseCard
                          rounded="lg"
                          class="peer-checked:!border-primary-500 peer-checked:!bg-primary-500/5 relative border-2 p-4 transition-all duration-300 hover:border-primary-500/50 hover:shadow-lg hover:shadow-primary-500/5 peer-checked:[&_.child]:!opacity-100 peer-checked:[&_.check-badge]:!scale-100"
                        >
                          <div class="relative">
                            <!-- Content -->
                            <div class="flex items-center sm:flex-col sm:items-center gap-3 sm:gap-2">
                              <div class="size-10 sm:size-12 rounded-xl bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center shrink-0">
                                <Icon name="ph:calendar-duotone" class="size-5 sm:size-6 text-primary-500" />
                              </div>
                              <div class="sm:text-center flex-1 sm:flex-none min-w-0">
                                <div class="font-semibold truncate">Daily Booking</div>
                                <div class="text-xs text-muted-500 mt-0.5 truncate">Perfect for longer stays</div>
                                <div class="flex items-center gap-1.5 text-xs text-primary-500 font-medium mt-1 sm:justify-center">
                                  <Icon name="ph:check-circle" class="size-4" />
                                  <span>Better value</span>
                                </div>
                              </div>
                            </div>

                            <!-- Check Badge -->
                            <div class="check-badge absolute -right-2 -top-2 scale-0 transition-transform duration-300">
                              <div class="size-6 rounded-full bg-primary-500 flex items-center justify-center">
                                <Icon name="ph:check-bold" class="size-3.5 text-white" />
                              </div>
                  </div>
                </div>
              </BaseCard>
                      </BaseRadioHeadless>

                      <BaseRadioHeadless
                        v-model="bookingType"
                        name="booking_type"
                        value="hourly"
                      >
                        <BaseCard
                          rounded="lg"
                          class="peer-checked:!border-primary-500 peer-checked:!bg-primary-500/5 relative border-2 p-4 transition-all duration-300 hover:border-primary-500/50 hover:shadow-lg hover:shadow-primary-500/5 peer-checked:[&_.child]:!opacity-100 peer-checked:[&_.check-badge]:!scale-100"
                        >
                          <div class="relative">
                            <!-- Content -->
                            <div class="flex items-center sm:flex-col sm:items-center gap-3 sm:gap-2">
                              <div class="size-10 sm:size-12 rounded-xl bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center shrink-0">
                                <Icon name="ph:clock-countdown-duotone" class="size-5 sm:size-6 text-primary-500" />
                  </div>
                              <div class="sm:text-center flex-1 sm:flex-none min-w-0">
                                <div class="font-semibold truncate">Hourly Booking</div>
                                <div class="text-xs text-muted-500 mt-0.5 truncate">Flexible short-term use</div>
                                <div class="flex items-center gap-1.5 text-xs text-primary-500 font-medium mt-1 sm:justify-center">
                                  <Icon name="ph:lightning" class="size-4" />
                                  <span>Quick access</span>
                                </div>
                              </div>
                            </div>

                            <!-- Check Badge -->
                            <div class="check-badge absolute -right-2 -top-2 scale-0 transition-transform duration-300">
                              <div class="size-6 rounded-full bg-primary-500 flex items-center justify-center">
                                <Icon name="ph:check-bold" class="size-3.5 text-white" />
                              </div>
                            </div>
                          </div>
                        </BaseCard>
                      </BaseRadioHeadless>
                    </div>

                    <!-- Feature Comparison -->
                    <div class="mt-4 grid grid-cols-2 gap-3 text-xs text-muted-500">
                      <div class="space-y-1.5">
                        <div class="flex items-center gap-1.5">
                          <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400 shrink-0" />
                          <span class="truncate">Business hours</span>
                        </div>
                        <div class="flex items-center gap-1.5">
                          <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400 shrink-0" />
                          <span class="truncate">Better daily rates</span>
                        </div>
                      </div>
                      <div class="space-y-1.5">
                        <div class="flex items-center gap-1.5">
                          <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400 shrink-0" />
                          <span class="truncate">No minimum stay</span>
                        </div>
                        <div class="flex items-center gap-1.5">
                          <Icon name="ph:check-circle" class="size-3.5 text-muted-500 dark:text-muted-400 shrink-0" />
                          <span class="truncate">Pay as you go</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Room Selection -->
                  <div>
                    <label class="block text-sm font-medium mb-2 flex items-center justify-between">
                      <span>Select Room</span>
                      <span class="text-xs text-muted-500">{{ spaceData.space.rooms?.length || 0 }} available</span>
                    </label>
                    <div class="relative">
                      <select 
                        v-model="selectedRoom" 
                        class="w-full px-3 py-2.5 bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg text-sm appearance-none pr-10"
                      >
                        <option 
                          v-for="room in spaceData.space.rooms" 
                          :key="room.id" 
                          :value="room"
                        >
                          {{ room.name }} ({{ room.type.type }}) - {{ formatPrice(bookingType === 'daily' ? room.pricePerDay : room.pricePerHour) }}
                        </option>
                      </select>
                      <Icon 
                        name="ph:caret-down-bold" 
                        class="absolute right-3 top-1/2 -translate-y-1/2 size-4 text-muted-500 pointer-events-none"
                      />
                  </div>
                </div>

                  <!-- Booking Dates -->
                  <div class="space-y-3 mb-6">
                    <div class="flex items-center justify-between">
                      <label class="text-sm font-medium">{{ bookingType === 'hourly' ? 'Date & Time' : 'Dates' }}</label>
                      <div class="flex items-center gap-2 text-xs px-2 py-1 rounded-full bg-primary-50 dark:bg-primary-500/10 text-primary-600 dark:text-primary-400">
                        <Icon :name="bookingType === 'hourly' ? 'ph:clock' : 'ph:calendar'" class="size-3.5" />
                        <span v-if="bookingType === 'hourly'">
                          {{ calculateHours }} {{ calculateHours === 1 ? 'hour' : 'hours' }}
                        </span>
                        <span v-else>
                          {{ totalNights }} {{ totalNights === 1 ? 'day' : 'days' }}
                        </span>
                      </div>
                    </div>
                    
                    <div class="grid" :class="bookingType === 'hourly' ? 'grid-cols-1' : 'grid-cols-2 gap-3'">
                      <!-- Single Date Selector for Hourly -->
                      <div v-if="bookingType === 'hourly'" class="space-y-3">
                        <!-- Date Selection -->
                        <div class="relative">
                          <button 
                            class="w-full px-3 py-2.5 text-left text-sm bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg hover:bg-muted-100 dark:hover:bg-muted-700 transition-colors"
                            @click="toggleCheckIn"
                          >
                            <div class="flex items-center justify-between">
                              <div>
                                <div class="text-xs text-muted-500">Date</div>
                                <div class="font-medium">{{ formatDate(selectedDates.checkIn) }}</div>
                              </div>
                              <Icon name="ph:calendar" class="size-4 text-muted-500" />
                            </div>
                          </button>
                          
                          <div 
                            v-if="isCheckInOpen"
                            class="absolute left-0 top-full mt-2 z-10"
                          >
                            <Calendar 
                              v-model="selectedDates.checkIn"
                              @dayclick="handleCheckInSelect"
                              :attributes="bookingCalendarAttrs"
                              :min-date="new Date()"
                              trim-weeks
                              class="vcal-booking"
                    />
                  </div>
                        </div>

                        <!-- Time Selection -->
                        <div class="grid grid-cols-2 gap-3">
                          <!-- Start Time -->
                          <div class="relative">
                            <div class="text-xs text-muted-500 mb-1.5">Start time</div>
                            <select
                              v-model="selectedTimes.startTime"
                              class="w-full px-3 py-2.5 text-sm bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg appearance-none cursor-pointer pr-10"
                            >
                              <option disabled value="">Select</option>
                              <option 
                                v-for="slot in availableStartTimes" 
                                :key="slot.value"
                                :value="slot.value"
                              >
                                {{ slot.label }}
                              </option>
                            </select>
                            <Icon 
                              name="ph:clock" 
                              class="absolute right-3 top-[calc(50%+4px)] -translate-y-1/2 size-4 text-muted-500 pointer-events-none"
                    />
                  </div>

                          <!-- End Time -->
                          <div class="relative">
                            <div class="text-xs text-muted-500 mb-1.5">End time</div>
                            <select
                              v-model="selectedTimes.endTime"
                              class="w-full px-3 py-2.5 text-sm bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg appearance-none cursor-pointer pr-10"
                              :disabled="!selectedTimes.startTime"
                              :class="!selectedTimes.startTime ? 'opacity-50' : ''"
                            >
                              <option disabled value="">Select</option>
                              <option 
                                v-for="slot in availableEndTimes" 
                                :key="slot.value"
                                :value="slot.value"
                              >
                                {{ slot.label }}
                              </option>
                            </select>
                            <Icon 
                              name="ph:clock" 
                              class="absolute right-3 top-[calc(50%+4px)] -translate-y-1/2 size-4 text-muted-500 pointer-events-none"
                            />
                </div>
                        </div>
                      </div>

                      <!-- Date Range Selector for Daily -->
                      <template v-else>
                        <!-- Check-in Date -->
                        <div class="relative">
                          <div class="text-xs text-muted-500 mb-1.5">Check in</div>
                          <button 
                            class="w-full px-3 py-2.5 text-left text-sm bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg hover:bg-muted-100 dark:hover:bg-muted-700 transition-colors"
                            @click="toggleCheckIn"
                          >
                            <div class="flex items-center justify-between">
                              <div class="font-medium">{{ formatDate(selectedDates.checkIn) }}</div>
                              <Icon name="ph:calendar" class="size-4 text-muted-500" />
                            </div>
                          </button>
                          
                          <div 
                            v-if="isCheckInOpen"
                            class="absolute left-0 top-full mt-2 z-10"
                          >
                            <Calendar 
                              v-model="selectedDates.checkIn"
                              @dayclick="handleCheckInSelect"
                              :attributes="bookingCalendarAttrs"
                              :min-date="new Date()"
                              trim-weeks
                              class="vcal-booking"
                            />
                          </div>
                        </div>
                        
                        <!-- Check-out Date -->
                        <div class="relative">
                          <div class="text-xs text-muted-500 mb-1.5">Check out</div>
                          <button 
                            class="w-full px-3 py-2.5 text-left text-sm bg-muted-50 dark:bg-muted-800 border border-muted-300 dark:border-muted-700 rounded-lg hover:bg-muted-100 dark:hover:bg-muted-700 transition-colors"
                            @click="toggleCheckOut"
                          >
                            <div class="flex items-center justify-between">
                              <div class="font-medium">{{ formatDate(selectedDates.checkOut) }}</div>
                              <Icon name="ph:calendar" class="size-4 text-muted-500" />
                            </div>
                          </button>
                          
                          <div 
                            v-if="isCheckOutOpen"
                            class="absolute right-0 top-full mt-2 z-10"
                          >
                            <Calendar 
                              v-model="selectedDates.checkOut"
                              @dayclick="handleCheckOutSelect"
                              :attributes="bookingCalendarAttrs"
                              :min-date="selectedDates.checkIn || new Date()"
                              trim-weeks
                              class="vcal-booking"
                            />
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>

                  <!-- Guests -->
                  <div class="mb-6">
                    <div class="flex items-center justify-between mb-2">
                      <label class="text-sm font-medium">Guests</label>
                      <span class="text-xs text-muted-500">
                        Max {{ selectedRoom?.capacity || 10 }} guests
                      </span>
                    </div>
                    <BaseInputNumber
                      v-model="guestCount"
                      :min="1"
                      :max="selectedRoom?.capacity || 10"
                      class="w-full"
                      contrast="muted"
                    />
                  </div>

                  <!-- Price Breakdown -->
                  <div class="mb-6 pt-4 border-t border-muted-200 dark:border-muted-800">
                    <div class="space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-muted-600 dark:text-muted-400">
                          {{ bookingType === 'hourly' 
                            ? `${formatPrice(selectedRoom?.pricePerHour || 0)} × ${calculateHours} hours` 
                            : `${formatPrice(selectedRoom?.pricePerDay || 0)} × ${totalNights} days`
                          }}
                        </span>
                        <span class="text-sm">{{ formatPrice(totalPrice.subtotal) }}</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-muted-600 dark:text-muted-400">Cleaning fee</span>
                        <span class="text-sm">{{ formatPrice(totalPrice.cleaningFee) }}</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-muted-600 dark:text-muted-400">Service fee</span>
                        <span class="text-sm">{{ formatPrice(totalPrice.serviceFee) }}</span>
                      </div>
                      <div v-if="totalPrice.addOnsCost > 0" class="flex items-center justify-between">
                        <span class="text-sm text-muted-600 dark:text-muted-400">Add-ons</span>
                        <span class="text-sm">{{ formatPrice(totalPrice.addOnsCost) }}</span>
                      </div>
                    </div>
                    <div class="flex items-center justify-between mt-4 pt-4 border-t border-muted-200 dark:border-muted-800">
                      <span class="font-medium">Total</span>
                      <span class="font-medium text-lg">{{ formatPrice(totalPrice.total) }}</span>
                    </div>
                  </div>

                  <!-- Availability Section -->
                  <div class="space-y-4">
                    <!-- Availability Indicator -->
                    <div 
                      v-if="hasSelectedDates && !isCheckingAvailability" 
                      class="p-4 rounded-xl text-sm flex items-start gap-3"
                      :class="[
                        isAvailable 
                          ? 'bg-success-50 dark:bg-success-500/10'
                          : 'bg-danger-50 dark:bg-danger-500/10'
                      ]"
                    >
                      <div 
                        class="size-8 rounded-full flex items-center justify-center shrink-0 mt-0.5"
                        :class="[
                          isAvailable 
                            ? 'bg-success-100 dark:bg-success-500/20 text-success-600 dark:text-success-400'
                            : 'bg-danger-100 dark:bg-danger-500/20 text-danger-600 dark:text-danger-400'
                        ]"
                      >
                        <Icon 
                          :name="isAvailable ? 'ph:check-circle' : 'ph:x-circle'" 
                          class="size-5"
                        />
                  </div>
                  <div>
                        <div class="font-medium mb-0.5" :class="[
                          isAvailable 
                            ? 'text-success-600 dark:text-success-400'
                            : 'text-danger-600 dark:text-danger-400'
                        ]">
                          {{ isAvailable ? 'Space Available' : 'Space Unavailable' }}
                  </div>
                        <div 
                          class="text-sm"
                          :class="[
                            isAvailable 
                              ? 'text-success-600/80 dark:text-success-400/80'
                              : 'text-danger-600/80 dark:text-danger-400/80'
                          ]"
                        >
                          <template v-if="isAvailable">
                            This space is available for your selected dates
                          </template>
                          <template v-else>
                            <template v-if="bookingType === 'hourly'">
                              Try selecting different hours or date
                            </template>
                            <template v-else>
                              Try adjusting your dates
                            </template>
                          </template>
                </div>
                      </div>
                    </div>

                    <!-- Checking Availability State -->
                    <div 
                      v-else-if="isCheckingAvailability"
                      class="p-4 rounded-xl bg-muted-100 dark:bg-muted-800 text-sm flex items-start gap-3"
                    >
                      <div class="size-8 rounded-full bg-primary-50 dark:bg-primary-500/10 flex items-center justify-center shrink-0 mt-0.5">
                        <Icon name="ph:circle-notch" class="size-5 animate-spin text-primary-500" />
                      </div>
                      <div>
                        <div class="font-medium mb-0.5">Checking Availability</div>
                        <div class="text-sm text-muted-500">Please wait while we verify the space availability</div>
                      </div>
                    </div>

                    <!-- Reserve Button -->
                    <button
                      @click="handleReserve"
                      :disabled="!isValidBooking || isCheckingAvailability"
                      class="w-full px-4 py-3.5 text-sm font-medium rounded-xl transition-all relative flex items-center justify-center gap-2"
                      :class="[
                        isValidBooking && !isCheckingAvailability
                          ? 'bg-primary-500 text-white hover:bg-primary-600 shadow-sm'
                          : 'bg-muted-200 dark:bg-muted-700 text-muted-500 cursor-not-allowed'
                      ]"
                    >
                      <Icon 
                        :name="isCheckingAvailability ? 'ph:circle-notch' : 'ph:check-circle'" 
                        class="size-4"
                        :class="{ 'animate-spin': isCheckingAvailability }"
                      />
                      <span v-if="isCheckingAvailability">
                        Checking availability...
                      </span>
                      <span v-else-if="bookingType === 'hourly' && !isValidTimeRange">
                        Please select valid time range
                      </span>
                      <span v-else-if="!isValidBooking && !isAvailable">
                        Complete booking information
                      </span>
                      <span v-else-if="!isAvailable">
                        Space not available
                      </span>
                      <span v-else>
                        Reserve space
                      </span>
                    </button>

                    <p class="text-xs text-center text-muted-500">
                      You won't be charged yet
                    </p>
                  </div>
                </div>
              </div>
            </BaseCard>

            <!-- Space Owner Contact Card -->
            <BaseCard color="muted" rounded="lg" class="mt-6">
              <div class="p-5">
                <div class="flex items-start gap-4">
                  <div class="shrink-0">
                    <div class="size-12 rounded-full bg-white dark:bg-muted-900/50 shadow-sm overflow-hidden">
                      <img 
                        :src="spaceData.space.owner?.photo?.path || spaceData.space.logo?.path || '/img/avatars/default-other.jpg'" 
                        :alt="spaceData.space.owner?.firstName"
                        class="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between gap-2 mb-1">
                      <h4 class="font-medium text-sm text-muted-800 dark:text-muted-200 truncate">
                        {{ spaceData.space.owner?.firstName }} {{ spaceData.space.owner?.lastName }}
                      </h4>
                      <span class="shrink-0 px-2 py-0.5 text-xs font-medium rounded-full bg-primary-50 dark:bg-primary-500/10 text-primary-600 dark:text-primary-400">
                        Host
                      </span>
                    </div>
                    <p class="text-xs text-muted-500 dark:text-muted-400 mb-3">
                      Member since {{ $dateFns.format(spaceData.space.owner?.createdAt, 'MMMM yyyy') }}
                    </p>
                    <div class="flex items-center gap-2">
                      <button class="px-3 py-1.5 text-xs font-medium rounded-lg bg-white dark:bg-muted-900/50 border border-muted-300 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-900 transition-colors flex items-center gap-1.5">
                        <Icon name="ph:envelope" class="size-3.5" />
                        Message
                      </button>
                      <button class="px-3 py-1.5 text-xs font-medium rounded-lg bg-white dark:bg-muted-900/50 border border-muted-300 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-900 transition-colors flex items-center gap-1.5">
                        <Icon name="ph:phone" class="size-3.5" />
                        Contact
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </BaseCard>
          </div>
        </div>
      </div>
    </div>

    <!-- Fallback for when no data is available but not in loading or error state -->
    <div v-else class="flex items-center justify-center min-h-[60vh]">
      <div class="text-center">
        <Icon name="ph:info-circle" class="size-16 text-muted-500 mb-4" />
        <h2 class="text-xl font-bold mb-2">No workspace data available</h2>
        <p class="text-muted-500 mb-4">The requested workspace could not be found</p>
        <BaseButton @click="$router.push('/explore')">Browse Workspaces</BaseButton>
      </div>
    </div>
  </div>
  
  <!-- Photo Gallery Modal -->
  <Teleport to="body">
    <Transition name="modal">
      <div 
        v-if="showAllPhotos" 
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-xl"
        @click="closePhotoGallery"
      >
        <!-- Gallery Controls -->
        <div class="absolute top-4 right-4 z-20 flex items-center gap-4">
          <div class="bg-black/40 backdrop-blur-md px-3 py-1.5 rounded-full text-white text-sm font-medium">
            {{ activeImageIndex + 1 }} / {{ allPhotos.length }}
          </div>
          <button 
            class="size-10 rounded-full bg-black/40 backdrop-blur-md text-white flex items-center justify-center hover:bg-black/60 transition-colors"
            @click.stop="closePhotoGallery"
          >
            <Icon name="ph:x" class="size-5" />
          </button>
        </div>
        
        <!-- Navigation Buttons -->
        <button 
          v-if="allPhotos.length > 1" 
          class="absolute left-4 top-1/2 -translate-y-1/2 size-12 rounded-full bg-black/40 backdrop-blur-md text-white flex items-center justify-center hover:bg-black/60 transition-colors z-20"
          @click.stop="navigateGallery(-1)"
          :disabled="isTransitioning"
        >
          <Icon name="ph:caret-left-bold" class="size-5" />
        </button>
        
        <button 
          v-if="allPhotos.length > 1" 
          class="absolute right-4 top-1/2 -translate-y-1/2 size-12 rounded-full bg-black/40 backdrop-blur-md text-white flex items-center justify-center hover:bg-black/60 transition-colors z-20"
          @click.stop="navigateGallery(1)"
          :disabled="isTransitioning"
        >
          <Icon name="ph:caret-right-bold" class="size-5" />
        </button>
        
        <!-- Image Container -->
        <div 
          class="relative w-full h-full flex items-center justify-center z-10 overflow-hidden"
          @click.stop="toggleZoom"
          @touchstart="handleTouchStart"
          @touchend="handleTouchEnd"
        >
          <Transition name="fade" mode="out-in">
            <img 
              :key="activeImageIndex"
              :src="allPhotos[activeImageIndex]" 
              :alt="`${spaceData.space.name} image ${activeImageIndex + 1}`"
              class="max-w-full max-h-full object-contain transition-transform duration-300"
              :class="{ 'cursor-zoom-in': !isZoomed, 'cursor-zoom-out scale-150': isZoomed }"
              @error="(e) => { console.error('Image failed to load:', allPhotos[activeImageIndex]); e.target.src = '/img/placeholder.png'; }"
            >
          </Transition>
          
          <!-- Usage instructions -->
          <div class="absolute bottom-6 left-1/2 -translate-x-1/2 bg-black/40 backdrop-blur-md px-3 py-1.5 rounded-full text-white text-xs flex items-center gap-2 pointer-events-none opacity-70">
            <Icon name="ph:arrows-horizontal" class="size-3" />
            <span>Swipe or use arrow keys</span>
            <span class="size-1 rounded-full bg-white/50"></span>
            <Icon name="ph:magnifying-glass-plus" class="size-3" />
            <span>Tap to zoom</span>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
  <ShareModal
    v-if="!pending"
    :isModalOpen="isModalOpen"
    :space="spaceData.space"
    :url="url"
    @close="closeModal"
  />

  <!-- Report Review Modal -->
  <TairoModal
    :open="isReportModalOpen"
    size="md"
    @close="closeReportModal"
  >
    <template #header>
      <div class="flex w-full items-center justify-between p-6">
        <div class="flex items-center gap-3">
          <div class="size-10 rounded-lg bg-danger-50 dark:bg-danger-500/10 flex items-center justify-center">
            <Icon name="ph:flag-duotone" class="size-5 text-danger-500" />
          </div>
          <div>
            <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
              Report Review
            </h3>
            <p class="text-sm text-muted-500">Help us maintain a safe community</p>
          </div>
        </div>
        <BaseButtonClose @click="closeReportModal" />
      </div>
    </template>

    <div class="px-6 pb-6">
      <form @submit.prevent="submitReport" class="space-y-6">
        <!-- Report Reason Selection -->
        <div class="space-y-3">
          <label class="text-sm font-medium text-muted-700 dark:text-muted-300">
            Why are you reporting this review? <span class="text-danger-500">*</span>
          </label>
          <div class="space-y-2">
            <label
              v-for="reason in reportReasons"
              :key="reason.value"
              class="flex cursor-pointer items-center rounded-lg border p-3 hover:border-muted-300 dark:hover:border-muted-600 transition-colors"
              :class="[
                reportForm.reason === reason.value
                  ? 'border-danger-300 dark:border-danger-600 bg-danger-50/50 dark:bg-danger-900/20'
                  : 'border-muted-200 dark:border-muted-700'
              ]"
            >
              <input
                type="radio"
                v-model="reportForm.reason"
                :value="reason.value"
                class="sr-only"
              >
              <Icon
                :name="reason.icon"
                class="size-5 text-danger-500 shrink-0"
              />
              <span class="ml-3 text-sm font-medium text-muted-700 dark:text-muted-300">
                {{ reason.label }}
              </span>
              <Icon
                v-if="reportForm.reason === reason.value"
                name="ph:check-circle-fill"
                class="ml-auto size-5 text-danger-500"
              />
            </label>
          </div>
        </div>

        <!-- Additional Details -->
        <div class="space-y-3">
          <label class="text-sm font-medium text-muted-700 dark:text-muted-300">
            Additional details (optional)
          </label>
          <BaseTextarea
            v-model="reportForm.description"
            placeholder="Please provide any additional context that might help us understand the issue..."
            rows="4"
            :classes="{
              wrapper: 'w-full',
              textarea: 'w-full bg-white dark:bg-muted-900 border border-muted-300 dark:border-muted-700 hover:border-muted-400 focus:border-danger-500 dark:focus:border-danger-500 rounded-lg text-sm resize-none'
            }"
          />
          <p class="text-xs text-muted-500">
            <Icon name="ph:info" class="size-3 mr-1" />
            Your report will be reviewed by our moderation team within 24 hours.
          </p>
        </div>
      </form>
    </div>

    <template #footer>
      <div class="p-6 pt-0">
        <div class="flex gap-3 justify-end">
          <BaseButton
            variant="outline"
            color="muted"
            @click="closeReportModal"
            :disabled="isSubmittingReport"
          >
            Cancel
          </BaseButton>
          <BaseButton
            color="danger"
            @click="submitReport"
            :disabled="!reportForm.reason || isSubmittingReport"
            :loading="isSubmittingReport"
          >
            <Icon v-if="!isSubmittingReport" name="ph:flag" class="size-4 mr-2" />
            {{ isSubmittingReport ? 'Submitting...' : 'Submit Report' }}
          </BaseButton>
        </div>
      </div>
    </template>
  </TairoModal>

  <!-- Claim Space Modal -->
  <TairoModal
    v-if="spaceData?.space"
    :open="isClaimModalOpen"
    size="lg"
    @close="closeClaimModal"
  >
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <div>
          <BaseHeading
            as="h3"
            size="md"
            weight="medium"
            class="text-muted-900 dark:text-white"
          >
            Claim This Space
          </BaseHeading>
          <BaseText size="xs" class="text-muted-400">
            Submit a request to claim ownership of this space
          </BaseText>
        </div>
        <BaseButtonClose @click="closeClaimModal" />
      </div>
    </template>

    <div class="p-4 md:p-6">
      <form class="space-y-4">
        <!-- Personal Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <BaseInput
            v-model="claimForm.fullName"
            label="Full Name"
            placeholder="Enter your full name"
            required
          />
          <BaseInput
            v-model="claimForm.email"
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            required
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <BaseInput
            v-model="claimForm.phoneNumber"
            label="Phone Number"
            placeholder="+212 6XX XXX XXX"
            required
          />
          <BaseInput
            v-model="claimForm.position"
            label="Position/Role"
            placeholder="e.g., Owner, Manager"
            required
          />
        </div>

        <!-- Business Information -->
        <div class="border-t border-muted-200 dark:border-muted-700 pt-4">
          <h4 class="font-medium text-muted-800 dark:text-white mb-3">Business Information</h4>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BaseInput
              v-model="claimForm.businessName"
              label="Business Name"
              placeholder="Enter business/company name"
              required
            />
            <BaseInput
              v-model="claimForm.businessRegistrationNumber"
              label="Registration Number"
              placeholder="Business registration or tax ID"
            />
          </div>
        </div>

        <!-- Message -->
        <BaseTextarea
          v-model="claimForm.message"
          label="Why are you claiming this space?"
          placeholder="Please explain your relationship to this space and why you should be granted ownership access..."
          rows="4"
          required
        />
      </form>
    </div>

    <template #footer>
      <div class="p-4 md:p-6">
        <div class="flex items-center justify-end gap-3">
          <BaseButton
            @click="closeClaimModal"
          >
            Cancel
          </BaseButton>
          <BaseButton
            color="primary"
            :loading="isSubmittingClaim"
            :disabled="!isClaimFormValid"
            @click="submitClaimRequest"
          >
            <Icon name="ph:paper-plane-duotone" class="size-4" />
            Submit Claim Request
          </BaseButton>
        </div>
      </div>
    </template>
  </TairoModal>

</template>

<style scoped>
/* Import v-calendar styles */
@import '~/assets/css/vcalendar.css';

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Add these styles to ensure proper calendar positioning */
.vcal-booking {
  background: var(--background);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Ensure the calendar container doesn't get cut off */
.vcal-booking .vc-container {
  width: 100%;
  min-width: 250px;
}

/* Add dark mode support */
:root[data-theme="dark"] .vcal-booking {
  --background: var(--muted-900);
  --border-color: var(--muted-700);
}

:root[data-theme="light"] .vcal-booking {
  --background: white;
  --border-color: var(--muted-200);
}

/* Style select dropdowns to match design */
select {
  background-image: none;
}

select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
