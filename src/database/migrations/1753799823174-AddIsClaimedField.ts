import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsClaimedField1753799823174 implements MigrationInterface {
  name = 'AddIsClaimedField1753799823174';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "space_claims" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "fullName" character varying(100) NOT NULL, "email" character varying(255) NOT NULL, "phoneNumber" character varying(20) NOT NULL, "position" character varying(100) NOT NULL, "businessName" character varying(200) NOT NULL, "businessRegistrationNumber" character varying(50), "message" text NOT NULL, "businessLicenseUrl" text, "identificationDocumentUrl" text, "proofOfOwnershipUrl" text, "status" character varying(20) NOT NULL DEFAULT 'pending', "adminNotes" text, "reviewedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "spaceId" uuid, "userId" integer, "reviewedById" integer, CONSTRAINT "PK_63006bbfffb8200b9c1599a3fa5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_49b9169d9ad83f00d049315037" ON "space_claims" ("email") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_37f7ae5a237dbe47a65a35f195" ON "space_claims" ("status") `,
    );
    await queryRunner.query(
      `ALTER TABLE "space" ADD "isClaimed" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0f806b3206cb8d02ed6f867d12" ON "space" ("isClaimed") `,
    );
    await queryRunner.query(
      `ALTER TABLE "space_claims" ADD CONSTRAINT "FK_f86920d226ae7638373a1f7d256" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "space_claims" ADD CONSTRAINT "FK_4b440f30b3ae8b2b966c78e4fa0" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "space_claims" ADD CONSTRAINT "FK_dde2e59f1e6576a87f4df63adeb" FOREIGN KEY ("reviewedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "space_claims" DROP CONSTRAINT "FK_dde2e59f1e6576a87f4df63adeb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "space_claims" DROP CONSTRAINT "FK_4b440f30b3ae8b2b966c78e4fa0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "space_claims" DROP CONSTRAINT "FK_f86920d226ae7638373a1f7d256"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0f806b3206cb8d02ed6f867d12"`,
    );
    await queryRunner.query(`ALTER TABLE "space" DROP COLUMN "isClaimed"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_37f7ae5a237dbe47a65a35f195"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_49b9169d9ad83f00d049315037"`,
    );
    await queryRunner.query(`DROP TABLE "space_claims"`);
  }
}
