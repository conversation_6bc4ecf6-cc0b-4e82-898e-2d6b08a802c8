import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsEmail,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON>ength,
  IsOptional,
} from 'class-validator';

export class ClaimSpaceDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the person claiming the space',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  fullName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the person claiming the space',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: '+212612345678',
    description: 'Phone number of the person claiming the space',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(10)
  @MaxLength(20)
  phoneNumber: string;

  @ApiProperty({
    example: 'Owner',
    description: 'Position/role of the person in relation to the space',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  position: string;

  @ApiProperty({
    example: 'Coworking Space LLC',
    description: 'Name of the business/company that owns the space',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(200)
  businessName: string;

  @ApiProperty({
    example: '*********',
    description: 'Business registration number or tax ID',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  businessRegistrationNumber?: string;

  @ApiProperty({
    example: 'I am the owner of this coworking space and would like to claim this listing to manage bookings and update information.',
    description: 'Detailed message explaining why the user is claiming this space',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(20)
  @MaxLength(1000)
  message: string;

  @ApiProperty({
    example: 'https://example.com/business-license.pdf',
    description: 'URL or path to business license document',
    required: false,
  })
  @IsOptional()
  @IsString()
  businessLicenseUrl?: string;

  @ApiProperty({
    example: 'https://example.com/id-document.pdf',
    description: 'URL or path to identification document',
    required: false,
  })
  @IsOptional()
  @IsString()
  identificationDocumentUrl?: string;

  @ApiProperty({
    example: 'https://example.com/proof-of-ownership.pdf',
    description: 'URL or path to proof of ownership document',
    required: false,
  })
  @IsOptional()
  @IsString()
  proofOfOwnershipUrl?: string;
}

export class ClaimSpaceResponseDto {
  @ApiProperty({
    example: 'claim-request-uuid',
    description: 'Unique identifier for the claim request',
  })
  claimRequestId: string;

  @ApiProperty({
    example: 'pending',
    description: 'Status of the claim request',
  })
  status: string;

  @ApiProperty({
    example: 'Your claim request has been submitted successfully. We will review it and get back to you within 2-3 business days.',
    description: 'Message confirming the submission',
  })
  message: string;
}
