import { Injectable, NotFoundException, HttpStatus } from '@nestjs/common';
import { CreateSpaceDto } from './dto/create-space.dto';
import { CreateSpaceWithRoomsDto } from './dto/create-space-with-rooms.dto';
import { UpdateSpaceDto } from './dto/update-space.dto';
import { ClaimSpaceDto, ClaimSpaceResponseDto } from './dto/claim-space.dto';
import { SpaceRepository } from './infrastructure/persistence/space.repository';
import { RoomRepository } from 'src/rooms/infrastructure/persistence/room.repository';
import { ContactRepository } from 'src/contacts/infrastructure/persistence/contact.repository';
import { OperationRepository } from 'src/operations/infrastructure/persistence/operation.repository';
import { OperationsService } from '../operations/operations.service';
import { SpaceTeamRoleRepository } from 'src/space-team-roles/infrastructure/persistence/space-team-role.repository';
import { IPaginationOptions } from '../utils/types/pagination-options';
import { Transactional } from 'typeorm-transactional';
import { Space } from './domain/space';
import { User } from '../users/domain/user';
import { Point } from 'geojson';
import { FeatureUsagesService } from '../feature-usages/feature-usages.service';
import { LoggerService } from '../infrastructure/logger/logger.service';
import { FileMapper } from '../files/infrastructure/persistence/relational/mappers/file.mapper';
import { MembershipsService } from './../memberships/memberships.service';
import { OnEvent } from '@nestjs/event-emitter';
import { ReviewCreatedEvent } from '../system-events/review-created.event';
import { DefaultApprovalRulesService } from '../bookings/default-approval-rules.service';

@Injectable()
export class SpacesService {
  constructor(
    private readonly spaceRepository: SpaceRepository,
    private readonly roomRepository: RoomRepository,
    private readonly operationRepository: OperationRepository,
    private readonly operationsService: OperationsService,
    private readonly spaceTeamRoleRepository: SpaceTeamRoleRepository,
    private readonly contactRepository: ContactRepository,
    private readonly featureUsagesService: FeatureUsagesService,
    private readonly logger: LoggerService,
    private readonly membershipsService: MembershipsService,
    private readonly defaultApprovalRulesService: DefaultApprovalRulesService,
  ) {
    this.logger.setContext('SpacesService');
  }

  async create(createSpaceDto: CreateSpaceDto, user: User) {
    this.logger.debug({
      message: 'Creating new space',
      userId: user.id,
      spaceName: createSpaceDto.name,
    });

    const pointObject: Point = {
      type: 'Point',
      coordinates: createSpaceDto.location.coordinates,
    };
    const clonedCreateSpaceDto = {
      ...createSpaceDto,
      location: pointObject,
      owner: user,
      logo: createSpaceDto.logo
        ? FileMapper.toPersistence(createSpaceDto.logo)
        : undefined,
      isClaimed: false, // New spaces are unclaimed by default
    };

    try {
      const createdSpace =
        await this.spaceRepository.create(clonedCreateSpaceDto);

      this.logger.log({
        message: 'Space created successfully',
        spaceId: createdSpace.id,
        userId: user.id,
      });

      // Create default approval rules for the new space
      await this.defaultApprovalRulesService.createDefaultRulesForSpace(
        createdSpace,
        user,
      );

      return createdSpace;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to create space',
          error: error.message,
          userId: user.id,
          spaceName: createSpaceDto.name,
        },
        error.stack,
      );
      throw error;
    }
  }

  async findAllWithPagination({
    paginationOptions,
  }: {
    paginationOptions: IPaginationOptions;
  }) {
    this.logger.debug({
      message: 'Fetching spaces with pagination',
      page: paginationOptions.page,
      limit: paginationOptions.limit,
    });

    try {
      const results = await this.spaceRepository.findAllWithPagination({
        paginationOptions: {
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
      });

      return results;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch spaces with pagination',
          error: error.message,
          page: paginationOptions.page,
          limit: paginationOptions.limit,
        },
        error.stack,
      );
      throw error;
    }
  }

  async findOne(id: Space['id']) {
    this.logger.debug({
      message: 'Fetching space by ID',
      spaceId: id,
    });

    try {
      const space = await this.spaceRepository.findById(id);
      if (space) {
        this.logger.log({
          message: 'Space found',
          spaceId: id,
        });
      } else {
        this.logger.warn({
          message: 'Space not found',
          spaceId: id,
        });
      }
      return space;
    } catch (error) {
      this.logger.error(
        {
          message: 'Error fetching space by ID',
          error: error.message,
          spaceId: id,
        },
        error.stack,
      );
      throw error;
    }
  }

  async update(id: Space['id'], updateSpaceDto: UpdateSpaceDto) {
    this.logger.debug({
      message: 'Updating space',
      spaceId: id,
      updateFields: Object.keys(updateSpaceDto),
    });

    try {
      const updatedSpace = await this.spaceRepository.update(
        id,
        updateSpaceDto,
      );
      this.logger.log({
        message: 'Space updated successfully',
        spaceId: id,
      });
      return updatedSpace;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to update space',
          error: error.message,
          spaceId: id,
        },
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: Space['id']) {
    this.logger.debug({
      message: 'Removing space',
      spaceId: id,
    });

    try {
      const result = await this.spaceRepository.remove(id);
      this.logger.log({
        message: 'Space removed successfully',
        spaceId: id,
      });
      return result;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to remove space',
          error: error.message,
          spaceId: id,
        },
        error.stack,
      );
      throw error;
    }
  }

  async getSpaceIdBySlug(slug: string) {
    this.logger.debug({
      message: 'Fetching space ID by slug',
      slug,
    });

    try {
      const spaceId = await this.spaceRepository.getSpaceIdBySlug(slug);
      if (spaceId) {
        this.logger.log({
          message: 'Space ID found',
          slug,
          spaceId,
        });
      } else {
        this.logger.warn({
          message: 'Space ID not found',
          slug,
        });
      }
      return spaceId;
    } catch (error) {
      this.logger.error(
        {
          message: 'Error fetching space ID by slug',
          error: error.message,
          slug,
        },
        error.stack,
      );
      throw error;
    }
  }

  async getOwnedSpaces(user: User) {
    this.logger.debug({
      message: 'Fetching owned spaces',
      userId: user.id,
    });

    try {
      const spaces = await this.spaceRepository.getOwnedSpaces(user);
      this.logger.log({
        message: 'Owned spaces fetched successfully',
        userId: user.id,
        count: spaces.length,
      });
      return spaces;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch owned spaces',
          error: error.message,
          userId: user.id,
        },
        error.stack,
      );
      throw error;
    }
  }

  async getOwnedSpacesWithTasks(user: User) {
    this.logger.debug({
      message: 'Fetching owned spaces with tasks',
      userId: user.id,
    });

    try {
      const spaces = await this.spaceRepository.getOwnedSpacesWithTasks(user);
      this.logger.log({
        message: 'Owned spaces with tasks fetched successfully',
        userId: user.id,
        count: spaces.length,
      });
      return spaces;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch owned spaces with tasks',
          error: error.message,
          userId: user.id,
        },
        error.stack,
      );
      throw error;
    }
  }

  async getOwnedSpacesForNavigation(user: User) {
    this.logger.debug({
      message: 'Fetching owned spaces for navigation',
      userId: user.id,
    });

    try {
      const spaces =
        await this.spaceRepository.getOwnedSpacesForNavigation(user);
      this.logger.log({
        message: 'Owned spaces fetched successfully',
        userId: user.id,
        count: spaces.length,
      });
      return spaces;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to fetch owned spaces for navigation',
          error: error.message,
          userId: user.id,
        },
        error.stack,
      );
      throw error;
    }
  }

  @Transactional()
  async createSpaceWithRoomsAndImages(
    createSpaceWithRoomsDto: CreateSpaceWithRoomsDto,
    user: User,
  ): Promise<Space> {
    const correlationId =
      Date.now().toString(36) + Math.random().toString(36).substr(2);

    console.log(
      'createSpaceWithRoomsDto from service',
      createSpaceWithRoomsDto,
    );

    this.logger.debug({
      message: 'Creating space with rooms and images',
      userId: user.id,
      roomCount: createSpaceWithRoomsDto.rooms.length,
      correlationId,
    });

    try {
      const canCreateSpace = await this.featureUsagesService.trackUsage(
        user.id,
        'space_management',
      );

      if (!canCreateSpace) {
        this.logger.warn({
          message: 'User permission/limit check failed',
          userId: user.id,
          feature: 'space_management',
          correlationId,
        });
        throw new Error(
          'User does not have permission to create space or limit reached',
        );
      }

      this.logger.debug({
        message: 'Creating base space',
        userId: user.id,
        correlationId,
      });
      const createdSpace = await this.create(createSpaceWithRoomsDto, user);

      this.logger.debug({
        message: 'Creating rooms for space',
        spaceId: createdSpace.id,
        roomCount: createSpaceWithRoomsDto.rooms.length,
        correlationId,
      });
      await this.roomRepository.createMany(
        createSpaceWithRoomsDto.rooms.map((room) => ({
          ...room,
          space: createdSpace,
        })),
      );

      if (
        createSpaceWithRoomsDto.hasMembership &&
        createSpaceWithRoomsDto.memberships
      ) {
        this.logger.debug({
          message: 'Creating memberships for space',
          spaceId: createdSpace.id,
          correlationId,
        });
        // loop through memberships and create them
        createSpaceWithRoomsDto.memberships.forEach(async (membership) => {
          await this.membershipsService.create({
            ...membership,
            space: createdSpace,
          });
        });
      }

      this.logger.debug({
        message: 'Creating operations for space',
        spaceId: createdSpace.id,
        correlationId,
      });
      await this.operationsService.create({
        ...createSpaceWithRoomsDto,
        space: createdSpace,
      });

      this.logger.debug({
        message: 'Creating contact for space',
        spaceId: createdSpace.id,
        correlationId,
      });
      await this.contactRepository.create({
        ...createSpaceWithRoomsDto.contact,
        space: createdSpace,
        firstName: user.firstName ? user.firstName : 'Space',
        lastName: user.lastName ? user.lastName : 'Owner',
      });

      if (createSpaceWithRoomsDto.teamMembers?.length) {
        this.logger.debug({
          message: 'Adding team members to space',
          spaceId: createdSpace.id,
          teamMemberCount: createSpaceWithRoomsDto.teamMembers.length,
          correlationId,
        });

        console.log(
          'createSpaceWithRoomsDto.teamMembers',
          createSpaceWithRoomsDto.teamMembers,
        );

        await Promise.all(
          createSpaceWithRoomsDto.teamMembers.map((teamMember) =>
            this.spaceTeamRoleRepository.addUserToSpaceTeam(
              String(teamMember.user.id),
              String(createdSpace.id),
              String(teamMember.role.id),
            ),
          ),
        );
      }

      this.logger.log({
        message: 'Space with rooms and images created successfully',
        spaceId: createdSpace.id,
        userId: user.id,
        roomCount: createSpaceWithRoomsDto.rooms.length,
        hasTeam: createSpaceWithRoomsDto.teamMembers?.length > 0,
        correlationId,
      });

      return createdSpace;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to create space with rooms and images',
          error: error.message,
          userId: user.id,
          correlationId,
        },
        error.stack,
      );
      throw error;
    }
  }

  async findBySlug(slug: string) {
    this.logger.debug({
      message: 'Finding space by slug',
      slug,
    });

    try {
      const space = await this.spaceRepository.findBySlug(slug);
      if (!space) {
        this.logger.warn({
          message: 'Space not found by slug',
          slug,
        });
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            space: 'spaceNotFound',
          },
        });
      }

      this.logger.log({
        message: 'Space found by slug',
        slug,
        spaceId: space.id,
      });

      return space;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Already logged above
        throw error;
      }

      this.logger.error(
        {
          message: 'Error finding space by slug',
          error: error.message,
          slug,
        },
        error.stack,
      );
      throw error;
    }
  }

  async getSpaceMemberships(spaceId: string) {
    this.logger.debug({
      message: 'Getting space memberships',
      spaceId,
    });

    try {
      const memberships =
        await this.membershipsService.getMembershipStatsBySpaceId(spaceId);
      return memberships;
    } catch (error) {
      this.logger.error(error);
    }
  }

  async findBySlugWithRelations(slug: string) {
    this.logger.debug({
      message: 'Finding space by slug with relations',
      slug,
    });

    try {
      const space = await this.spaceRepository.findBySlugWithRelations(slug);
      if (!space) {
        this.logger.warn({
          message: 'Space not found by slug with relations',
          slug,
        });
        throw new NotFoundException({
          status: HttpStatus.NOT_FOUND,
          errors: {
            space: 'spaceNotFound',
          },
        });
      }

      this.logger.log({
        message: 'Space found by slug with relations',
        slug,
        spaceId: space.id,
      });

      return space;
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Already logged above
        throw error;
      }

      this.logger.error(
        {
          message: 'Error finding space by slug with relations',
          error: error.message,
          slug,
        },
        error.stack,
      );
      throw error;
    }
  }

  async findNearby(
    latitude: number,
    longitude: number,
    radiusInKm: number,
    filters?: {
      priceMin?: number;
      priceMax?: number;
      rating?: number;
      spaceTypes?: string[];
      amenities?: string[];
      hasMembership?: boolean;
    },
  ) {
    this.logger.debug({
      message: 'Finding nearby spaces',
      latitude,
      longitude,
      radiusInKm,
      filters,
    });

    // Log the filters being applied
    if (filters) {
      this.logger.debug({
        message: 'Applying filters',
        priceMin: filters.priceMin,
        priceMax: filters.priceMax,
        rating: filters.rating,
        spaceTypes: filters.spaceTypes,
        amenities: filters.amenities,
      });
    }

    console.log(
      'SpacesService.findNearby called with filters:',
      JSON.stringify(filters, null, 2),
    );

    try {
      const spaces = await this.spaceRepository.findNearby(
        latitude,
        longitude,
        radiusInKm,
        filters,
      );

      this.logger.log({
        message: 'Nearby spaces found',
        latitude,
        longitude,
        radiusInKm,
        filters,
        count: spaces.length,
      });

      return spaces;
    } catch (error) {
      this.logger.error(
        {
          message: 'Error finding nearby spaces',
          error: error.message,
          latitude,
          longitude,
          radiusInKm,
          filters,
        },
        error.stack,
      );
      throw error;
    }
  }

  async isOwner(spaceId: string, userId: string) {
    this.logger.debug({
      message: 'Checking if user is owner of space',
      spaceId,
      userId,
    });

    try {
      const isOwner = await this.spaceRepository.isOwner(spaceId, userId);
      return isOwner;
    } catch (error) {
      this.logger.error(
        {
          message: 'Error checking if user is owner of space',
          error: error.message,
          spaceId,
          userId,
        },
        error.stack,
      );
      throw error;
    }
  }

  async updateSpaceTotalReviewsAndAverageRating(
    spaceId: string,
    totalReviews: number,
    averageRating: number,
  ) {
    this.logger.debug({
      message: 'Updating space total reviews and average rating',
      spaceId,
      totalReviews,
      averageRating,
    });

    try {
      const updatedSpace = await this.spaceRepository.update(spaceId, {
        totalReviews,
        rating: averageRating,
      });
      return updatedSpace;
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to update space total reviews and average rating',
          error: error.message,
          spaceId,
          totalReviews,
          averageRating,
        },
        error.stack,
      );
      throw error;
    }
  }

  @OnEvent('review.created')
  async handleReviewCreatedEvent(event: ReviewCreatedEvent) {
    this.logger.debug({
      message: 'Handling review created event',
      spaceId: event.review.space.id,
      totalReviews: event.totalReviews,
      averageRating: event.rating,
    });

    try {
      await this.updateSpaceTotalReviewsAndAverageRating(
        event.review.space.id,
        event.totalReviews,
        event.rating,
      );
    } catch (error) {
      this.logger.error(
        {
          message: 'Failed to handle review created event',
          error: error.message,
          spaceId: event.review.space.id,
          totalReviews: event.totalReviews,
          rating: event.rating,
        },
        error.stack,
      );
    }
  }

  /**
   * Get the total capacity of a space (sum of all room capacities)
   */
  async getSpaceCapacity(spaceId: string): Promise<number> {
    try {
      const space = await this.spaceRepository.findById(spaceId);
      if (!space) {
        this.logger.warn({
          message: 'Space not found for capacity calculation',
          spaceId,
        });
        return 0;
      }

      // Get all rooms for the space and sum their capacities
      const rooms = await this.roomRepository.getRoomsBySpace(space);
      const totalCapacity = rooms.reduce((sum, room) => {
        return sum + (room.capacity || 0);
      }, 0);

      this.logger.debug({
        message: 'Space capacity calculated',
        spaceId,
        totalCapacity,
        roomCount: rooms.length,
      });

      return totalCapacity;
    } catch (error) {
      this.logger.error(
        {
          message: 'Error calculating space capacity',
          error: error.message,
          spaceId,
        },
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Submit a claim request for a space
   */
  async claimSpace(
    spaceId: string,
    claimSpaceDto: ClaimSpaceDto,
    user: User,
  ): Promise<ClaimSpaceResponseDto> {
    this.logger.debug({
      message: 'Processing space claim request',
      spaceId,
      userId: user.id,
      email: claimSpaceDto.email,
    });

    try {
      // Check if space exists
      const space = await this.spaceRepository.findById(spaceId);
      if (!space) {
        throw new NotFoundException('Space not found');
      }

      // Check if space is already claimed
      if (space.isClaimed) {
        throw new Error('This space has already been claimed');
      }

      // Check if user already has a pending claim for this space
      // This would require a SpaceClaimRepository, but for now we'll skip this check

      // Create claim request ID
      const claimRequestId = `claim-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Here you would typically:
      // 1. Save the claim request to database
      // 2. Send notification to admins
      // 3. Send confirmation email to user

      this.logger.log({
        message: 'Space claim request submitted successfully',
        spaceId,
        userId: user.id,
        claimRequestId,
        businessName: claimSpaceDto.businessName,
      });

      return {
        claimRequestId,
        status: 'pending',
        message:
          'Your claim request has been submitted successfully. We will review it and get back to you within 2-3 business days.',
      };
    } catch (error) {
      this.logger.error(
        {
          message: 'Error processing space claim request',
          error: error.message,
          spaceId,
          userId: user.id,
        },
        error.stack,
      );
      throw error;
    }
  }
}
